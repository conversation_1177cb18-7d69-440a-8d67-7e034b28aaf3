import { z } from 'zod';

/**
 * Define all possible capabilities in the system
 */
export enum Capability {
  // Section-level capabilities (broader permissions)
  MANAGE_ADMINS = 'manage_admins',
  MANAGE_SUPER_ADMINS = 'manage_super_admins',
  MANAGE_DOCTORS = 'manage_doctors',
  MANAGE_PATIENTS = 'manage_patients',
  MANAGE_PHARMACIES = 'manage_pharmacies',
  MANAGE_PRODUCTS = 'manage_products',
  MANAGE_PRODUCT_PRICES = 'manage_product_prices',
  MANAGE_PRODUCT_PRICE_EQUIVALENCE = 'manage_product_price_equivalence',
  MANAGE_PRODUCT_PRICE_MAPPING = 'manage_product_price_mapping',
  MANAGE_TREATMENTS = 'manage_treatments',
  MANAGE_BILLING = 'manage_billing',
  MANAGE_STATES = 'manage_states',

  // Admin management
  VIEW_ADMINS = 'view_admins',
  CREATE_ADMINS = 'create_admins',
  EDIT_ADMINS = 'edit_admins',
  DEACTIVATE_ADMINS = 'deactivate_admins',
  REACTIVATE_ADMINS = 'reactivate_admins',
  DELETE_ADMINS = 'delete_admins',

  // Doctor management
  VIEW_DOCTORS = 'view_doctors',
  CREATE_DOCTORS = 'create_doctors',
  EDIT_DOCTORS = 'edit_doctors',
  DEACTIVATE_DOCTORS = 'deactivate_doctors',
  REACTIVATE_DOCTORS = 'reactivate_doctors',

  // Patient management
  VIEW_PATIENTS = 'view_patients',
  EDIT_PATIENT_INFO = 'edit_patient_info',
  EDIT_PATIENT_ADDRESSES = 'edit_patient_addresses',
  RESET_PATIENT_PASSWORD = 'reset_patient_password',
  TRANSFER_PATIENT_DOCTOR = 'transfer_patient_doctor',
  TRANSFER_PATIENT_PHARMACY = 'transfer_patient_pharmacy',
  CREATE_PATIENT_NOTES = 'create_patient_notes',
  DELETE_PATIENTS = 'delete_patients',

  // Pharmacy management
  VIEW_PHARMACIES = 'view_pharmacies',
  CREATE_PHARMACIES = 'create_pharmacies',
  EDIT_PHARMACIES = 'edit_pharmacies',
  DEACTIVATE_PHARMACIES = 'deactivate_pharmacies',
  REACTIVATE_PHARMACIES = 'reactivate_pharmacies',
  DELETE_PHARMACIES = 'delete_pharmacies',
  TRANSFER_PHARMACY_PATIENTS = 'transfer_pharmacy_patients',

  // Product management
  VIEW_PRODUCTS = 'view_products',
  CREATE_PRODUCTS = 'create_products',
  EDIT_PRODUCTS = 'edit_products',
  ACTIVATE_PRODUCTS = 'activate_products',
  DEACTIVATE_PRODUCTS = 'deactivate_products',
  DELETE_PRODUCTS = 'delete_products',

  // Product price management
  VIEW_PRODUCT_PRICES = 'view_product_prices',
  CREATE_PRODUCT_PRICES = 'create_product_prices',
  EDIT_PRODUCT_PRICES = 'edit_product_prices',
  DELETE_PRODUCT_PRICES = 'delete_product_prices',

  // Product price equivalence management
  VIEW_PRODUCT_PRICE_EQUIVALENCE = 'view_product_price_equivalence',
  CREATE_PRODUCT_PRICE_EQUIVALENCE = 'create_product_price_equivalence',
  EDIT_PRODUCT_PRICE_EQUIVALENCE = 'edit_product_price_equivalence',
  DELETE_PRODUCT_PRICE_EQUIVALENCE = 'delete_product_price_equivalence',

  // Product price mapping management
  VIEW_PRODUCT_PRICE_MAPPING = 'view_product_price_mapping',
  CREATE_PRODUCT_PRICE_MAPPING = 'create_product_price_mapping',
  EDIT_PRODUCT_PRICE_MAPPING = 'edit_product_price_mapping',
  DELETE_PRODUCT_PRICE_MAPPING = 'delete_product_price_mapping',

  // Treatment management
  VIEW_TREATMENTS = 'view_treatments',
  CREATE_TREATMENTS = 'create_treatments',
  CANCEL_TREATMENTS = 'cancel_treatments',
  FIRE_NEXT_TREATMENT = 'fire_next_treatment',
  MOVE_REFILL_DATE = 'move_refill_date',

  // Billing management
  VIEW_BILLING = 'view_billing',
  PROCESS_REFUNDS = 'process_refunds',
  MANAGE_PAYMENT_METHODS = 'manage_payment_methods',

  // State management
  VIEW_STATES = 'view_states',
  EDIT_STATES = 'edit_states',

  // General capabilities
  VIEW_DASHBOARD = 'view_dashboard',

  // Insights
  VIEW_INSIGHTS = 'view_insights',
}

// Zod validator for capabilities
export const capabilitySchema = z.nativeEnum(Capability);

// Zod validator for arrays of capabilities
export const capabilitiesArraySchema = z.array(capabilitySchema);
