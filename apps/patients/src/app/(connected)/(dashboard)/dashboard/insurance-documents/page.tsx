'use client';

import MaxWidthWrapper from '@/components/MaxWidthWrapper';
import DashboardTitle from '@/components/patient-dashboard/DashboardTitle';

import {
  CardDownloadHsaDoc,
  CardDownloadRxDoc,
} from '~/components/ui/card-download-insurance';

const InsuranceDocumentsList = () => {
  return (
    <MaxWidthWrapper className="flex flex-1 flex-col md:max-w-[2000px] md:px-0">
      <div className="flex flex-1 flex-col md:my-16 md:mb-20 md:gap-4 lg:mx-28 lg:gap-20">
        <div className="flex flex-1 flex-col gap-10 md:px-6 lg:flex-row lg:gap-20">
          <div className="hidden w-full flex-col justify-between md:flex md:w-3/4">
            <DashboardTitle title="Insurance Documents" />
          </div>

          <div className="flex h-[500px] w-full flex-1 flex-col md:flex-auto">
            <CardDownloadHsaDoc />
            <CardDownloadRxDoc />
            We understand that some of our patients may be looking to submit
            their medical expenses to their insurance provider for
            reimbursement. To assist with this process, we have made it easy for
            you to access the necessary documents, such as the Health Savings
            Account (HSA). By accessing these documents and submitting them to
            your insurance provider, you may be able to recoup some of the costs
            of your medical treatment. Please note that the submission and
            management of these claims is the patient's responsibility.
          </div>
        </div>
      </div>
    </MaxWidthWrapper>
  );
};

export default InsuranceDocumentsList;
