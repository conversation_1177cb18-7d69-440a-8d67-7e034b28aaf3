import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PatientPaymentMethodPersistence {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: Prisma.PatientPaymentMethodCreateInput) {
    return this.prisma.patientPaymentMethod.create({
      data: {
        ...data,
      },
    });
  }

  async update(id: string, data: Prisma.PatientPaymentMethodUpdateInput) {
    return this.prisma.patientPaymentMethod.update({
      where: { id },
      data,
    });
  }
  async getDefaultPaymentmethod(patientId: string) {
    return this.prisma.patientPaymentMethod.findFirst({
      where: {
        patientId: patientId,
        default: true,
      },
    });
  }
}
