import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class ChatWorker {
  private readonly logger = new Logger(ChatWorker.name);

  constructor(private readonly prisma: PrismaService) {}

  @Cron(CronExpression.EVERY_5_MINUTES)
  async fixMissingConversations() {
    const patients = await this.prisma.patient.findMany({
      where: {
        doctorId: { not: null },
        status: { notIn: ['cancelled', 'deleted'] },
        OR: [
          {
            conversation: null,
          },
          {
            conversation: {
              watcher: {
                // Less than 2 watchers
                none: {
                  id: {
                    not: undefined,
                  },
                  AND: {
                    id: {
                      not: undefined,
                    },
                  },
                },
              },
            },
          },
        ],
      },
      include: {
        user: true,
        doctor: true,
        conversation: {
          include: {
            watcher: true,
          },
        },
      },
    });

    this.logger.debug(
      `Found ${patients.length} patients with missing conversations`,
    );

    for (const patient of patients) {
      this.logger.debug(
        `Fixing patient ${patient.user.email} status ${patient.status}`,
      );

      // continue;
      await this.prisma.$transaction(async (tx) => {
        const conversation = await tx.conversation.findFirst({
          where: {
            userId: patient.userId,
          },
        });

        let conversationId = null;
        if (!conversation) {
          const c = await tx.conversation.create({
            data: {
              userId: patient.userId,
              patientId: patient.id,
            },
          });
          conversationId = c.id;
        } else {
          conversationId = conversation.id;
        }

        const watchers = await tx.conversationWatcher.findMany({
          where: { conversationId },
        });

        if (watchers.length < 2) {
          await tx.conversationWatcher.upsert({
            where: {
              conversationId_userId: {
                conversationId,
                userId: patient.userId,
              },
            },
            create: {
              userId: patient.userId,
              conversationId,
            },
            update: {},
          });

          await tx.conversationWatcher.upsert({
            where: {
              conversationId_userId: {
                conversationId,
                userId: patient.doctor.userId,
              },
            },
            create: {
              userId: patient.doctor.userId,
              conversationId,
            },
            update: {},
          });
        }
      });
    }
  }
}
