import { PreventImpersonatedEditGuard } from '@modules/auth/guards/prevent-impersonated-edit.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { MessageDto } from '@modules/chat/dto/message.dto';
import { GetConversationMessagesUseCase } from '@modules/chat/use-cases/get-conversation-messages.use-case';
import { MarkAsReadUseCase } from '@modules/chat/use-cases/mark-as-read.use-case';
import { SendMessageUseCase } from '@modules/chat/use-cases/send-message.use-case';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

import { PrismaService } from '../prisma/prisma.service';
import { SendMessageInput } from './types/chat.types';
import { GetConversationWatcherUseCase } from './use-cases/get-conversation-watcher.use-case';
import { ImageUploadGetPreSignedUrlUseCase } from './use-cases/image-upload-get-pre-signed-url.use-case';

@Controller('chat')
@UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
@Roles([roles.Patient, roles.Doctor])
export class ChatController {
  constructor(
    private readonly getConversationMessagesUseCase: GetConversationMessagesUseCase,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly markAsReadUseCase: MarkAsReadUseCase,
    private readonly imageUploadGetPreSignedUrlUseCase: ImageUploadGetPreSignedUrlUseCase,
    private readonly getConversationWatcherUseCase: GetConversationWatcherUseCase,
    private readonly prisma: PrismaService,
  ) {}

  @Get('/:conversationId/get-upload-url/:fileName')
  async getUploadUrl(
    @Param('fileName') fileName: string,
    @Param('conversationId') conversationId: string,
  ) {
    try {
      return await this.imageUploadGetPreSignedUrlUseCase.execute(
        conversationId,
        fileName,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }
  /**
   * for patients and doctors, but they need to know the conversationId
   * Doctor knows it because it has the list of conversations
   * Patient TBD
   * @param conversationId
   * @param request
   */
  @Get('/:conversationId')
  async getConversation(
    @Param('conversationId') conversationId: string,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];

      return await this.getConversationMessagesUseCase.execute(
        conversationId,
        userId,
      );
    } catch (e) {
      if (e.code === 'P2025') {
        throw new NotFoundException('Conversation not found');
      }
      throw new Error(e.message);
    }
  }

  @Post('/:conversationId/send')
  async sendMessage(
    @Body() message: MessageDto,
    @Param('conversationId') conversationId: string,
    @Req() request: Request,
  ) {
    return await this.sendMessageUseCase.execute({
      ...message,
      conversationId,
      userId: request.user['userId'],
      role: request.user['role'],
      needsReply: message.needsReply,
      type: 'message',
    });
  }

  @Post('/:conversationId/read')
  async markAsRead(
    @Param('conversationId') conversationId: string,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      return await this.markAsReadUseCase.execute(conversationId, userId);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('/:conversationId/watcher/:userId')
  async getConversationWatcher(
    @Param('conversationId') conversationId: string,
    @Param('userId') userId: string,
  ) {
    return await this.getConversationWatcherUseCase.execute(
      conversationId,
      userId,
    );
  }
  @Post('/:conversationId/needsReplyToggle')
  async needsReplyToggle(
    @Body()
    body: {
      userId: string;
      needsReply: boolean;
    },
    @Param('conversationId') conversationId: string,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      const userRole: string = request.user['role'];
      return await this.sendMessageUseCase.updateNeedsReply(
        this.prisma,
        {
          userId,
          conversationId,
          role: userRole,
        } as SendMessageInput,
        body.needsReply,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }
}
