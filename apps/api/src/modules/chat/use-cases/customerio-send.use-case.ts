import { ExecutionContext } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { CustomerioDto } from '@modules/chat/dto/customerio.dto';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { ChatImageService } from '../services/chat.image.service';
import { PatientMessageRouterService } from '../services/patient-message-router.service';
import { SendMessageUseCase } from './send-message.use-case';

interface sendCustomerIoMessage extends CustomerioDto {
  type: 'system' | 'doctorNote';
}
@Injectable()
export class CustomerioSendUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly sendMessageUseCase: SendMessageUseCase,
  ) {}

  async execute(payload: sendCustomerIoMessage, context?: ExecutionContext) {
    const { conversationId, doctorUserId } = await this.getDataForMessage(
      payload.userId,
    );

    await this.sendMessageUseCase.execute({
      content: payload.content,
      contentType: 'text',
      conversationId,
      type: payload.type,
      userId: doctorUserId,
      needsReply: false,
      role: 'Doctor',
    });
  }

  private async getDataForMessage(patientUserId: string) {
    const patient = await this.prisma.patient.findFirst({
      where: { id: patientUserId },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        conversation: true,
      },
    });
    if (!patient || !patient?.conversation || !patient?.doctor) {
      throw new NotFoundException('Patient, conversation, or doctor not found');
    }
    const conversationId = patient.conversation.id;
    const doctorUserId = patient.doctor.userId;

    return {
      conversationId,
      doctorUserId,
    };
  }
}
