import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { PatientUpdatedQueueEvent } from '@/modules/shared/events/patient-topic.definition';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  @SnsConsume({
    topic: 'patient-updated',
    consumerGroup: 'create-chat-service',
    filter: ['doctor_assigned'],
  })
  async handleSnsMessage({ payload }: PatientUpdatedQueueEvent) {
    const { patient } = payload;
    return this.execute({
      doctorUserId: patient.doctor.userId,
      patientUserId: patient.id,
    });
  }

  async execute({
    doctorUserId,
    patientUserId,
  }: {
    doctorUserId: string;
    patientUserId: string;
  }) {
    // check if conversation exists for the patient
    const existingConversation =
      await this.prismaService.conversation.findFirst({
        where: { userId: patientUserId },
        include: { watcher: true },
      });

    const exists =
      existingConversation &&
      existingConversation.watcher.some(
        (watcher) => watcher.userId === doctorUserId,
      ) &&
      existingConversation.watcher.some(
        (watcher) => watcher.userId === patientUserId,
      );

    if (exists) return existingConversation.id;

    // create conversation
    return this.prismaService.$transaction(async (prisma) => {
      const patient = await prisma.patient.findUnique({
        where: { userId: patientUserId },
      });

      const conversation = await prisma.conversation.upsert({
        where: { userId: patientUserId },
        create: {
          userId: patientUserId,
          patientId: patient.id,
          updatedAt: null,
        },
        update: {},
      });

      // create watchers for both patient and doctor
      await prisma.conversationWatcher.upsert({
        where: {
          conversationId_userId: {
            conversationId: conversation.id,
            userId: patientUserId,
          },
        },
        create: { userId: patientUserId, conversationId: conversation.id },
        update: {},
      });

      await prisma.conversationWatcher.upsert({
        where: {
          conversationId_userId: {
            conversationId: conversation.id,
            userId: doctorUserId,
          },
        },
        create: { userId: doctorUserId, conversationId: conversation.id },
        update: {},
      });

      return conversation.id;
    });
  }
}
