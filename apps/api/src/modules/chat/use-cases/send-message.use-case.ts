import { ExecutionContext, runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@/modules/prisma/prisma.service';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@/modules/shared/events';
import { roles } from '@modules/auth/types/roles';
import { ChatEvent } from '@modules/chat/events/chat.events';
import { SendMessageInput } from '@modules/chat/types/chat.types';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

import { AuditLog } from '@willow/utils/audit-log';

import { ChatImageService } from '../services/chat.image.service';
import { PatientMessageRouterService } from '../services/patient-message-router.service';

type GetConversationWatchersReturnType = Awaited<
  ReturnType<SendMessageUseCase['getConversationWatchers']>
>;
type CreateMessageReturnType = Awaited<
  ReturnType<SendMessageUseCase['createMessage']>
>;

@Injectable()
export class SendMessageUseCase {
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly prisma: PrismaService,
    private readonly chatImageService: ChatImageService,
    private readonly auditService: AuditService,
    private readonly patientMessageRouterService: PatientMessageRouterService,
    private readonly treatmentService: TreatmentService,
  ) {}

  async execute(message: SendMessageInput, context?: ExecutionContext) {
    const member = await this.getConversationMember(
      message.conversationId,
      message.userId,
      context?.prisma,
    );
    if (!member)
      throw new BadRequestException(
        'User is not a member of this conversation',
      );

    const record = await runInDbTransaction(
      context?.prisma || this.prisma,
      async (prisma) => {
        const newMessage = await this.createMessage(prisma, message);
        //if the message comes from a Patient
        // let the messageRouter the decision of updating the conversationWatcher (and intercom)
        if (message.role === roles.Patient) {
          await this.patientMessageRouterService.flagForProcessing(
            newMessage.conversationId,
          );
        } else {
          await this.updateConversationWatchers(prisma, message);
        }

        await this.handleNeedsReply(prisma, message);
        const conversation = await this.updateConversation(prisma, message);

        void this.auditService.append({
          patientId: conversation.patientId,
          action: 'CONVERSATION_CHAT_MESSAGE_CREATED',
          actorType: member.user.type.toUpperCase() as AuditLog['actorType'],
          actorId:
            member.user.doctor?.id ??
            member.user.patient?.id ??
            member.user.admin?.id,
          resourceType: 'CONVERSATION',
          resourceId: message.conversationId,
          details: {
            userId: message.userId,
            type: message.type,
            contentType: message.contentType,
            content: message.content,
          },
        });

        return newMessage;
      },
    );

    const watchers = await this.getConversationWatchers(
      message.conversationId,
      context?.prisma,
    );
    await this.emitEvents(watchers, record, message);

    return record;
  }

  private async getConversationMember(
    conversationId: string,
    userId: string,
    prisma?: PrismaTransactionalClient,
  ) {
    const client = prisma || this.prisma;
    return client.conversationWatcher.findFirst({
      where: { conversationId, userId },
      include: {
        user: {
          include: {
            doctor: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            patient: {
              select: {
                id: true,
                idPhoto: true,
                facePhoto: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            admin: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
          },
        },
      },
    });
  }

  private async createMessage(
    prisma: PrismaTransactionalClient,
    message: SendMessageInput,
  ) {
    const messageId: string = uuidv4();
    let messageContent = message.content;
    if (['file', 'image'].includes(message.contentType)) {
      const { destinationKey } =
        await this.chatImageService.moveFileToPermanentStorage(
          message.conversationId,
          messageId,
          message.content,
        );
      messageContent = destinationKey;
    }

    // Get the conversation to find the patient ID
    const conversation = await prisma.conversation.findFirst({
      where: { id: message.conversationId },
      select: { patientId: true },
    });

    if (!conversation) {
      throw new Error(`Conversation ${message.conversationId} not found.`);
    }

    // Check for active core treatment
    let treatmentId: string | undefined;
    const activeTreatment =
      await this.treatmentService.getActiveTreatmentByPatientId(
        conversation.patientId,
        true,
        { prisma },
      );

    // Check if treatment meets criteria
    if (
      activeTreatment &&
      activeTreatment.isCore &&
      (activeTreatment.status.startsWith('inProgress.') ||
        activeTreatment.status === 'failed' ||
        activeTreatment.status === 'paused')
    ) {
      treatmentId = activeTreatment.id;
    }

    return prisma.conversationMessage.create({
      data: {
        id: messageId,
        content: messageContent,
        conversationId: message.conversationId,
        userId: message.type === 'message' ? message.userId : null,
        contentType: message.contentType,
        type: message.type,
        ...(treatmentId && { treatmentId }),
      },
    });
  }

  private async updateConversationWatchers(
    prisma: PrismaTransactionalClient,
    message: SendMessageInput,
  ) {
    // Handle doctor note messages differently
    if (message.type === 'doctorNote') {
      // Get all doctors in the conversation
      const doctorWatchers = await prisma.conversationWatcher.findMany({
        where: {
          conversationId: message.conversationId,
          user: { type: 'doctor' },
        },
        include: { user: true },
      });

      // Update only doctor watchers (except the sender)
      for (const watcher of doctorWatchers) {
        await prisma.conversationWatcher.update({
          where: { id: watcher.id },
          data: { unreadMessages: { increment: 1 }, updatedAt: new Date() },
        });
      }

      return;
    }

    // Regular message handling (non-doctorNote)
    await prisma.conversationWatcher.updateMany({
      where: {
        conversationId: message.conversationId,
        userId: { not: message.userId },
      },
      data: { unreadMessages: { increment: 1 }, updatedAt: new Date() },
    });

    if (message.type == 'system' && message.role == roles.Doctor) return;
    await prisma.conversationWatcher.updateMany({
      where: { conversationId: message.conversationId, userId: message.userId },
      data: { unreadMessages: 0, updatedAt: new Date() },
    });
  }

  private async handleNeedsReply(
    prisma: PrismaTransactionalClient,
    message: SendMessageInput,
  ) {
    if (message.needsReply === undefined) return;

    if (message.role === roles.Doctor && message.needsReply) {
      await this.updateNeedsReply(prisma, message, true);
    } else if (message.role === roles.Patient) {
      await this.updateNeedsReply(prisma, message, false);
    }
  }

  async updateNeedsReply(
    prisma: PrismaTransactionalClient,
    message: SendMessageInput,
    needsReply: boolean,
  ) {
    await prisma.conversationWatcher.updateMany({
      where: {
        conversationId: message.conversationId,
        userId:
          message.role == roles.Doctor
            ? { not: message.userId }
            : message.userId,
      },
      data: { needsReply },
    });

    const conversation = await prisma.conversation.findFirst({
      where: { id: message.conversationId },
      select: { patientId: true },
    });

    if (!conversation)
      throw new Error(`Conversation ${message.conversationId} not found.`);

    const identifyEvent: SegmentIdentify = {
      userId: conversation.patientId,
      traits: { needsReply },
    };

    this.eventEmitter.emit(
      segmentIdentifyEvent.analyticIdentify,
      identifyEvent,
    );
  }

  private async updateConversation(
    prisma: PrismaTransactionalClient,
    message: SendMessageInput,
  ) {
    const lastMessageText =
      message.contentType === 'text' ? message.content : 'Attachment';

    return prisma.conversation.update({
      where: { id: message.conversationId },
      data: {
        lastMessageText,
        lastMessageFrom: message.type === 'message' ? message.userId : null,
        updatedAt: new Date(),
      },
    });
  }

  private async getConversationWatchers(
    conversationId: string,
    prisma?: PrismaTransactionalClient,
  ) {
    const client = prisma || this.prisma;
    return client.conversationWatcher.findMany({
      where: { conversationId },
      include: {
        user: {
          include: {
            doctor: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            patient: {
              select: {
                id: true,
                doctorId: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
          },
        },
      },
    });
  }

  private async emitEvents(
    watchers: GetConversationWatchersReturnType,
    record: CreateMessageReturnType,
    message: SendMessageInput,
  ) {
    const patient = watchers.find((watcher) => watcher.user.patient);
    const doctor = watchers.find(
      (watcher) =>
        patient.user.patient?.doctorId &&
        watcher.user.doctor?.id &&
        patient.user.patient?.doctorId === watcher.user.doctor?.id,
    );

    if (!doctor.user || !patient.user)
      throw new Error('Doctor or patient not found in conversation watchers.');

    const emitEvent =
      message.role === roles.Doctor
        ? segmentTrackEvents.doctorSentMessage
        : segmentTrackEvents.patientSentMessage;

    const eventProperties = {
      doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
      doctorID: doctor.user.doctor.id,
      patientName: `${patient.user.firstName} ${patient.user.lastName}`,
      patientID: patient.user.patient.id,
      type: message.type === 'system' ? 'auto' : 'manual',
    };

    const trackEvent: SegmentTrack = {
      event: emitEvent.name,
      userId: patient.user.id,
      properties: eventProperties,
    };

    this.eventEmitter.emit(emitEvent.event, trackEvent);

    const userIds: string[] = watchers.reduce((idsList, watcher) => {
      //dont send doctor's notes to patients
      if (watcher.user.type == 'patient' && message.type == 'doctorNote') {
        return idsList;
      }
      idsList.push(watcher.userId);
      return idsList;
    }, [] as string[]);
    this.eventEmitter.emit('chat.message', new ChatEvent(userIds, record));
  }
}
