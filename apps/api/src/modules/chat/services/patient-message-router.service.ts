import { PrismaService } from '@/modules/prisma/prisma.service';
import { AiService } from '@modules/ai/ai.service';
import { IntercomService } from '@modules/intercom/intercom.service';
import { SqsConsume } from '@modules/shared/aws/sqs/sqs.decorator';
import { Injectable, Logger } from '@nestjs/common';
import { addMinutes } from 'date-fns';
import { z } from 'zod';

const validInquiryTypes = [
  'SIDE_EFFECTS',
  'DOSAGE_QUESTION',
  'EFFICACY_CONCERN',
  'SYMPTOM_REPORT',
  'MEDICAL_QUESTION',
  'PRESCRIPTION_RENEWAL',
  'TREATMENT_APPROVAL',
  'SHIPPING_INQUIRY',
  'BILLING_QUESTION',
  'ACCOUNT_ISSUE',
  'SCHEDULING',
  'CONTACT_UPDATE',
  'GENERAL_INQUIRY',
  'ACKNOWLEDGMENT',
  'GREETING',
  'OTHER',
] as const;

const inferredMessageTargetResponseSchema = z.object({
  relevantForDoctor: z.boolean(),
  relevantForPatientServices: z.boolean(),
  reason: z.string(),
  inquiryTypes: z.array(z.enum(validInquiryTypes)),
  relevantMessageIndices: z.array(z.number().int().min(0)),
  messageSummary: z.string(),
});

interface MessageData {
  user: string;
  message: string;
  date: string;
  id?: string;
}

interface PatientMessagesResponse {
  messages: MessageData[];
  count: number;
}

const topic = (name: string) => name as any;

@Injectable()
export class PatientMessageRouterService {
  private readonly logger = new Logger(PatientMessageRouterService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly aiService: AiService,
    private readonly intercomService: IntercomService,
  ) {}

  @SqsConsume(topic('patient-message-router'), {
    batchSize: 1,
    maxRetries: 3,
    deadLetterQueueName: 'patient-message-router-dlq',
  })
  async handleSqsMessage(message: any) {
    const conversationRouterId = message.payload.conversationRouterId;
    const conversationRouter =
      await this.prismaService.conversationRouter.findFirst({
        where: { id: conversationRouterId },
        include: {
          conversation: {
            include: {
              patient: {
                include: { doctor: { include: { user: true } }, user: true },
              },
            },
          },
        },
      });

    const lastPatientMessages = await this.getPatientMessagesAfterDoctor(
      conversationRouter.conversation.id,
    );
    const messagesTarget = await this.analyzePatientMessages(
      lastPatientMessages.messages,
    );

    // Extract message IDs from the messages that were actually processed by the LLM
    const patientMessageIds = lastPatientMessages.messages
      .filter((msg) => msg.user === 'patient' && msg.id !== undefined)
      .map((msg) => msg.id as string);

    let intercomId = null;
    if (messagesTarget.relevantForPatientServices) {
      const user = conversationRouter.conversation.patient.user;

      // Extract only relevant patient messages for Intercom using the indices provided by the AI
      const relevantMessages = messagesTarget.relevantMessageIndices
        .filter(
          (index) => index >= 0 && index < lastPatientMessages.messages.length,
        )
        .map((index) => lastPatientMessages.messages[index])
        .filter((msg) => msg.user === 'patient') // Only include patient messages
        .map((msg) => msg.message);

      // If no relevant messages were found, fall back to all patient messages
      let messageContent =
        relevantMessages.length > 0
          ? relevantMessages.join('\n\n')
          : lastPatientMessages.messages
              .filter((msg) => msg.user === 'patient')
              .map((msg) => msg.message)
              .join('\n\n');

      // Get the doctor's last name
      const doctorLastName =
        conversationRouter.conversation.patient.doctor?.user?.lastName ||
        'the doctor';

      // Prepare content for Intercom with summary at the top if it exists and is relevant
      if (
        messagesTarget.messageSummary &&
        messagesTarget.messageSummary !==
          'No patient services inquiries in this conversation.'
      ) {
        // Different forwarded message based on relevance
        const forwardedText = messagesTarget.relevantForDoctor
          ? `[ Forwarded to/from Dr. ${doctorLastName} ]`
          : `[ Forwarded from Dr. ${doctorLastName} ]`;

        messageContent = `${messagesTarget.messageSummary}\n\n${forwardedText}\n\nOriginal Message:\n${messageContent}`;
      }

      const result = await this.intercomService.createConversation(
        user,
        messageContent,
      );
      intercomId = result?.message?.id || null;
    }
    if (messagesTarget.relevantForDoctor) {
      const doctorUserId: string =
        conversationRouter.conversation.patient.doctor.userId;
      await this.updateDoctorConversationWatchers(
        doctorUserId,
        conversationRouter.conversation.id,
        lastPatientMessages,
      );
    }

    // Update the conversation router record
    await this.prismaService.conversationRouter.update({
      where: { id: conversationRouterId },
      data: {
        status: 'processed',
        intercomId,
        messages: patientMessageIds,
        reason: messagesTarget.reason,
        relevantForPatientServices: messagesTarget.relevantForPatientServices,
        relevantForDoctor: messagesTarget.relevantForDoctor,
        inquiryTypes: messagesTarget.inquiryTypes,
      },
    });

    // Get the actual message IDs for the relevant messages
    // The relevantMessageIndices from the LLM response directly correspond to the indices
    // in the lastPatientMessages.messages array that we sent to the LLM
    if (messagesTarget.relevantMessageIndices.length > 0) {
      // Extract the message IDs directly from the messages array using the indices
      const relevantMessageIds = messagesTarget.relevantMessageIndices
        .filter(
          (index) => index >= 0 && index < lastPatientMessages.messages.length,
        )
        .map((index) => lastPatientMessages.messages[index].id)
        .filter((id) => id !== undefined) as string[];

      if (relevantMessageIds.length === 0) {
        this.logger.log('No valid relevant message IDs found');
        return;
      }

      // Update the conversation messages with the conversation router ID
      await this.prismaService.conversationMessage.updateMany({
        where: {
          id: { in: relevantMessageIds },
        },
        data: {
          conversationRouterId: conversationRouterId,
        },
      });

      // Log if we couldn't find IDs for some indices
      const missingIndices = messagesTarget.relevantMessageIndices.filter(
        (index) =>
          index >= 0 &&
          index < lastPatientMessages.messages.length &&
          !lastPatientMessages.messages[index].id,
      );

      if (missingIndices.length > 0) {
        this.logger.warn(
          `Missing IDs for message indices: ${missingIndices.join(', ')}`,
        );
      }
    }
  }

  async flagForProcessing(conversationId: string) {
    const routerUpdate = await this.prismaService.conversationRouter.updateMany(
      {
        where: { conversationId: conversationId, status: 'pending' },
        data: { delayedUntil: addMinutes(new Date(), 5) },
      },
    );

    if (routerUpdate.count > 0) return;
    await this.prismaService.conversationRouter.create({
      data: {
        conversationId: conversationId as string,
        status: 'pending',
        delayedUntil: addMinutes(new Date(), 5),
      },
    });
  }

  /**
   * Marks a conversation router as closed when an Intercom conversation is closed
   * @param intercomId The Intercom conversation ID
   * @returns The updated conversation router record, or null if not found
   */
  async markConversationRouterAsClosed(intercomId: string) {
    this.logger.log(
      `Marking conversation router as closed for Intercom ID: ${intercomId}`,
    );

    try {
      // Find the conversation router with the given Intercom ID
      const conversationRouter =
        await this.prismaService.conversationRouter.findFirst({
          where: { intercomId },
        });

      if (!conversationRouter) {
        this.logger.warn(
          `No conversation router found with Intercom ID: ${intercomId}`,
        );
        return null;
      }

      // Update the conversation router status to 'closed'
      const updatedRouter = await this.prismaService.conversationRouter.update({
        where: { id: conversationRouter.id },
        data: { status: 'closed' },
      });

      this.logger.log(
        `Successfully marked conversation router ${updatedRouter.id} as closed`,
      );
      return updatedRouter;
    } catch (error) {
      this.logger.error(
        `Error marking conversation router as closed for Intercom ID ${intercomId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Reopens a closed conversation router when an Intercom conversation is reopened
   * Only changes the status if the current status is 'closed'
   * @param intercomId The Intercom conversation ID
   * @returns The updated conversation router record, or null if not found or not closed
   */
  async reopenClosedConversationRouter(intercomId: string) {
    this.logger.log(
      `Reopening closed conversation router for Intercom ID: ${intercomId}`,
    );

    try {
      // Find the conversation router with the given Intercom ID
      const conversationRouter =
        await this.prismaService.conversationRouter.findFirst({
          where: { intercomId },
        });

      if (!conversationRouter) {
        this.logger.warn(
          `No conversation router found with Intercom ID: ${intercomId}`,
        );
        return null;
      }

      // Only update if the current status is 'closed'
      if (conversationRouter.status !== 'closed') {
        this.logger.warn(
          `Conversation router ${conversationRouter.id} is not closed (current status: ${conversationRouter.status}), skipping reopen`,
        );
        return conversationRouter;
      }

      // Update the conversation router status to 'processed'
      const updatedRouter = await this.prismaService.conversationRouter.update({
        where: { id: conversationRouter.id },
        data: { status: 'processed' },
      });

      this.logger.log(
        `Successfully reopened conversation router ${updatedRouter.id} (changed status from 'closed' to 'processed')`,
      );
      return updatedRouter;
    } catch (error) {
      this.logger.error(
        `Error reopening conversation router for Intercom ID ${intercomId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  public async analyzePatientMessages(messages: MessageData[]) {
    const systemPrompt = `You are an AI assistant trained to analyze patient-doctor conversations in a medical context.
    Your task is to accurately classify each conversation thread based on its content, context, and timing.

    # IMPORTANT CLASSIFICATION RULE:
    When in doubt, classify as "Medical Issues (Relevant for Doctor)". 
    It's better to have a doctor review a non-medical issue than to miss a medical concern.
    Only classify as "Administrative Issues" when you are CERTAIN the issue is purely logistical with NO medical implications.

    # Classification Criteria:

    ## Medical Issues (Relevant for Doctor):
    - Questions or concerns about medical treatment or medication
    - Reports of side effects or symptoms
    - Questions about dosage adjustments or side effects
    - Medical advice requests
    - Progress updates on treatment efficacy
    - New health concerns that may relate to treatment
    - Any mention of how the patient is feeling physically
    - Direct responses to doctor's questions or confirmations of any kind
    - Requests for additional refills (beyond simple administrative refills)
    - Any questions relating to dosage or side effects of their treatment

    ## Administrative Issues (Relevant for Patient Services):
    - Scheduling or appointment logistics ONLY
    - Medication shipping or delivery inquiries WITHOUT medical concerns
    - Billing, insurance, or payment questions
    - Address or contact information changes
    - Technical issues with patient portal or apps
    - Simple refill requests with NO medical concerns mentioned
    - General service inquiries
    - Pharmacy destinations or prescription transfers to different pharmacies

    ## Neither Category:
    - Simple acknowledgments (e.g., "Thank you", "OK")
    - Basic greetings without substance
    - Empty messages or purely social exchanges
    - Messages with no actionable content

    # Strong Indicators:

    ## Strong Medical Indicators:
    Keywords: pain, symptoms, side effects, bleeding, reaction, feel, worse, better, dizzy, nausea, concern, worried, dosage, dose
    Phrases: "is it normal", "should I be concerned", "when will it work", "I'm experiencing", "since I started", "yes doctor", "confirmed", "I understand", "additional refill"

    ## Strong Administrative Indicators:
    Keywords: tracking, invoice, portal, password, delivery, address, ship, billing, payment, insurance, pharmacy, transfer
    Phrases: "hasn't arrived", "billing department", "update my information", "track my package", "login issues", "send to pharmacy", "different pharmacy", "transfer prescription"

    # Common Edge Cases:
    - Prescription refills: If ONLY requesting a refill → Administrative. If mentioning ANY symptoms, side effects, or dosage concerns → Medical. Additional refills beyond standard → Medical.
    - "Not working" complaints: If about medication efficacy → Medical. If about website/portal → Administrative.
    - Shipping delays affecting medication: Both categories (patient needs medication AND shipping help)
    - Treatment approval with logistics: Both categories (medical decision AND administrative action)
    - Direct responses to doctor questions: Always Medical (even simple "yes" or "I confirm")
    - Pharmacy transfers: If ONLY about sending to different pharmacy → Administrative. If includes medical questions → Both.
    - Dosage questions: Always Medical, regardless of context

    # Examples:

    ## Example 1: MEDICAL ONLY
    [
      {"user": "patient", "message": "I've been taking the medication for 3 days and I'm getting headaches", "date": "2024-01-15T10:00:00Z"},
      {"user": "doctor", "message": "Headaches can be a side effect. Try taking it with food", "date": "2024-01-15T14:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Patient reporting side effects requires medical attention

    ## Example 2: ADMINISTRATIVE ONLY
    [
      {"user": "patient", "message": "My medication hasn't arrived yet. Can you check the tracking number?", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: false, relevantForPatientServices: true
    Reason: Pure shipping inquiry with no medical concerns

    ## Example 3: BOTH CATEGORIES
    [
      {"user": "patient", "message": "I'm ready to start the treatment the doctor recommended, but I need it shipped to my new address at 123 Main St?", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: true
    Reason: Contains treatment approval (medical) and address update (administrative)

    ## Example 4: MEDICAL (Disguised as Administrative)
    [
      {"user": "patient", "message": "I need a refill of my medication. I've been feeling more anxious lately and running out", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Refill request includes symptom report (anxiety) requiring medical review

    ## Example 5: MEDICAL (Doctor Response)
    [
      {"user": "doctor", "message": "Have you been taking the medication as prescribed?", "date": "2024-01-15T10:00:00Z"},
      {"user": "patient", "message": "Yes, I have", "date": "2024-01-15T11:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Direct response to doctor's question requires medical attention

    ## Example 6: ADMINISTRATIVE (Pharmacy Transfer)
    [
      {"user": "patient", "message": "Can you send my prescription to CVS on Main Street instead of Walgreens?", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: false, relevantForPatientServices: true
    Reason: Pure pharmacy transfer request with no medical concerns

    ## Example 7: MEDICAL (Dosage Question)
    [
      {"user": "patient", "message": "Should I take the medication with food or on an empty stomach?", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Question about dosage and medication administration requires medical guidance

    # Message Analysis Instructions:

    You will analyze a JSON array of messages with this structure:
    [
      {
        "user": "patient" or "doctor",
        "message": "the message content",
        "date": "ISO date string"
      }
    ]

    Consider:
    - Message sender (patient vs doctor) and conversation flow
    - Time gaps: >48 hours may indicate new topic
    - Recent messages (last 72 hours) carry more weight
    - Focus on the most recent patient inquiry

    # Inquiry Type Classification:

    ## Medical Inquiry Types:
    - "SIDE_EFFECTS": Patient reporting side effects or adverse reactions
    - "DOSAGE_QUESTION": Questions about medication dosage or adjustments
    - "EFFICACY_CONCERN": Concerns about whether the treatment is working
    - "SYMPTOM_REPORT": Reporting new or ongoing symptoms
    - "MEDICAL_QUESTION": General medical questions about the treatment
    - "PRESCRIPTION_RENEWAL": Requests for prescription renewal
    - "TREATMENT_APPROVAL": Patient approving or requesting to proceed with treatment

    ## Administrative Inquiry Types:
    - "SHIPPING_INQUIRY": Questions about medication shipping or delivery
    - "BILLING_QUESTION": Questions about billing, payments, or insurance
    - "ACCOUNT_ISSUE": Problems with patient portal or account access
    - "SCHEDULING": Appointment scheduling or changes
    - "CONTACT_UPDATE": Updates to contact information or address
    - "GENERAL_INQUIRY": General administrative questions

    ## Other Types:
    - "ACKNOWLEDGMENT": Simple acknowledgments or thank you messages
    - "GREETING": Basic greetings without substantive content
    - "OTHER": Any inquiry that doesn't fit the above categories

    # Response Requirements:

    For relevantMessageIndices:
    - Include ONLY patient messages relevant for PATIENT SERVICES
    - Use 0-based indexing
    - Return empty array if no administrative messages exist

    For messageSummary:
    - Write in third person ("The patient is asking about...")
    - Focus ONLY on administrative matters, but be sure to be descriptive and don't lose any of the information intent from the patient.
    - If no administrative inquiries exist, state: "No patient services inquiries in this conversation."

    REMEMBER: When uncertain, classify as medical. Patient safety is the top priority.`;
    try {
      const prompt = `Analyze this conversation and determine for whom the following messages are relevant: \n${JSON.stringify(messages, null, 2)}`;

      return await this.aiService.structuredPromptWithToolUse(
        systemPrompt,
        prompt,
        inferredMessageTargetResponseSchema,
      );
    } catch (error) {
      this.logger.error('Error getting message target from AI:', error);
      throw error;
    }
  }

  /**
   * Retrieves patient messages after the last doctor message in a conversation
   * Each message in the returned array includes its database ID, which allows us to
   * directly reference these messages later without having to refetch them
   */
  private async getPatientMessagesAfterDoctor(
    conversationId: string,
  ): Promise<PatientMessagesResponse> {
    // Get the last message in the conversation (from any user type)
    const lastMessage = await this.prismaService.conversationMessage.findFirst({
      where: {
        conversationId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        user: true,
      },
    });

    // Get the last doctor message in the conversation
    const lastDoctorMessage =
      await this.prismaService.conversationMessage.findFirst({
        where: { conversationId, user: { type: 'doctor' } },
        orderBy: { createdAt: 'desc' },
        include: { user: true },
      });

    // Determine which messages to retrieve:
    // - If last message is from doctor, return empty (no patient messages after doctor)
    // - If last message is from patient and there's a doctor message, get patient messages after doctor's last message
    // - If there's no doctor message, get all patient messages

    // If the last message is from the doctor, no patient messages to process
    if (lastMessage?.user?.type === 'doctor') {
      return {
        messages: [],
        count: 0,
      };
    }

    // Get all relevant messages in a single query
    // If there's a last doctor message, include it and all messages after it
    // Otherwise get recent messages
    const messagesQuery = {
      where: {
        conversationId,
        ...(lastDoctorMessage?.id && {
          OR: [
            { id: lastDoctorMessage.id }, // Include the last doctor message
            { createdAt: { gt: lastDoctorMessage.createdAt } }, // And all messages after it
          ],
        }),
      },
      orderBy: { createdAt: 'asc' as const },
      include: { user: true },
      take: 20, // Limit to recent messages
    };

    const messages =
      await this.prismaService.conversationMessage.findMany(messagesQuery);

    // Format messages as required, including the message ID for later reference
    const formattedMessages: MessageData[] = messages.map((message) => ({
      user: message.user?.type || 'unknown',
      message: message.content,
      date: message.createdAt.toISOString(),
      id: message.id, // Include the database ID
    }));

    // Count only patient messages for the count field
    const patientMessageCount = messages.filter(
      (message) => message.user?.type === 'patient',
    ).length;

    return {
      messages: formattedMessages,
      count: patientMessageCount,
    };
  }

  private async updateDoctorConversationWatchers(
    doctorUserId: string,
    conversationId: string,
    lastPatientMessages: PatientMessagesResponse,
  ) {
    await this.prismaService.conversationWatcher.updateMany({
      where: {
        userId: doctorUserId,
        conversationId: conversationId,
      },
      data: {
        updatedAt: new Date(),
        unreadMessages: { increment: lastPatientMessages.count },
      },
    });
  }
}
