import { CanActivate, ExecutionContext, mixin, Type } from '@nestjs/common';

export const HeaderAuthGuard = (
  headerKey: string,
  expectedValue: string | undefined,
): Type<any> => {
  class HeaderAuthGuardMixin implements CanActivate {
    private expectedValue: string;

    constructor() {
      if (!expectedValue) throw new Error(`Missing expected value`);
      this.expectedValue = expectedValue;
    }

    canActivate(context: ExecutionContext): boolean {
      const request = context.switchToHttp().getRequest();
      const authHeader = request.get(headerKey);

      if (!authHeader || authHeader !== this.expectedValue) return false;
      return true;
    }
  }

  return mixin(HeaderAuthGuardMixin);
};
