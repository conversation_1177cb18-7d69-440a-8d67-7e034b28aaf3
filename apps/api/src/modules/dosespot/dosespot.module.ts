import { DosespotService } from '@modules/dosespot/dosespot.service';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { DosespotController } from './dosespot.controller';
import { DosespotWebhookEventUseCase } from './use-cases/dosespot-webhook-event.use-case';
import { MarkAsCompleteSyncUseCase } from './use-cases/mark-as-complete-sync.use-case';

@Module({
  providers: [
    DosespotWebhookEventUseCase,
    MarkAsCompleteSyncUseCase,
    DosespotService,
  ],
  controllers: [DosespotController],
  exports: [DosespotService],
  imports: [PrismaModule],
})
export class DosespotModule {}
