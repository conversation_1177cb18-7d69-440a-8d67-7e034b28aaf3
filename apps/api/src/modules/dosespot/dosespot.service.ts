import { createHash, randomBytes } from 'crypto';
import * as querystring from 'querystring';
import {
  DosespotClinician,
  DosespotPatient,
  DosespotPatientResult,
  DosespotPharmacySearchResult,
  DosespotRegistrationStatusResponse,
  DosespotRegistrationStatusType,
} from '@modules/dosespot/types/dosespot-patient';
import { normalizeText } from '@modules/shared/helpers/generic';
import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import { differenceInYears } from 'date-fns';

interface DosespotToken {
  access_token: string;
  expires_in: number;
}

interface DosespotTokenKeys {
  [userId: string]: {
    access_token: string;
    expires_in: number;
  };
}

@Injectable()
export class DosespotService {
  private dosespotToken: DosespotTokenKeys = {};
  private tokenPromises: { [userId: string]: Promise<string> } = {};

  constructor() {}

  /**
   * Extract error message from DoseSpot API response
   * Handles both regular error messages and ModelState validation errors
   */
  private extractErrorMessage(errorData: any): string {
    // Handle ModelState validation errors
    if (errorData?.ModelState && typeof errorData.ModelState === 'object') {
      const validationErrors: string[] = [];

      for (const [, errors] of Object.entries(errorData.ModelState)) {
        if (Array.isArray(errors)) {
          errors.forEach((error: string) => {
            validationErrors.push(error.trim().replace(/\.$/, ''));
          });
        }
      }

      if (validationErrors.length > 0) {
        return `DoseSpot validation: ${validationErrors.join(', ')}`;
      }
    }

    // Handle regular error messages
    if (errorData?.Result?.ResultDescription) {
      return errorData.Result.ResultDescription;
    }

    if (errorData?.Message) {
      return errorData.Message;
    }

    if (errorData?.message) {
      return errorData.message;
    }

    return null;
  }

  async createClinician(data: {
    Prefix?: string;
    FirstName: string;
    MiddleName?: string;
    LastName: string;
    Suffix?: string;
    DateOfBirth: Date;
    Email: string;
    Address1: string;
    Address2?: string;
    City: string;
    State: string;
    ZipCode: string;
    PrimaryPhone: string;
    PrimaryPhoneType: number;
    PrimaryFax?: string;
    NPINumber: string;
    ClinicianRoleType?: number[];
    EPCSRequested?: boolean;
    Active?: boolean;
    PDMPRoleType?: number;
    ClinicianSpecialtyType?: number;
  }): Promise<{
    Id: number;
    Result: { ResultCode: string; ResultDescription: string };
  }> {
    try {
      // Get token using clinic key since we don't have a clinician ID yet
      const accessToken = await this.fetchToken(process.env.DOSESPOT_ADMIN_ID);

      const dataWithDefaults = {
        ...data,
        ClinicianRoleType: data.ClinicianRoleType || [1],
        Active: data.Active !== undefined ? data.Active : true,
        EPCSRequested:
          data.EPCSRequested !== undefined ? data.EPCSRequested : false,
      };

      const normalizedData = this.normalizeData(dataWithDefaults);

      const response = await axios.post(
        `${process.env.DOSESPOT_URL}/api/clinicians`,
        normalizedData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (response.data.Result?.ResultCode !== 'OK') {
        console.error('Clinician not created: ', response.data);
        throw new BadRequestException(
          'Clinician not created: ' + response.data.Result?.ResultDescription,
        );
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to create clinician in DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async updateClinician(
    clinicianId: string,
    data: Partial<{
      Prefix?: string;
      FirstName: string;
      MiddleName?: string;
      LastName: string;
      Suffix?: string;
      DateOfBirth: Date;
      Email: string;
      Address1: string;
      Address2?: string;
      City: string;
      State: string;
      ZipCode: string;
      PrimaryPhone: string;
      PrimaryPhoneType?: number;
      PrimaryFax?: string;
      ClinicianRoleType?: number[];
      Active?: boolean;
    }>,
  ): Promise<{
    Id: number;
    Result: { ResultCode: string; ResultDescription: string };
  }> {
    try {
      // Get token
      const accessToken = await this.getToken(process.env.DOSESPOT_ADMIN_ID);

      // Get current clinician data
      const currentClinician = await this.getClinician(clinicianId);
      if (!currentClinician) {
        throw new BadRequestException('Clinician not found in DoseSpot');
      }

      // Set default values for ClinicianRoleType if not provided
      const dataWithDefaults = {
        ...data,
        ClinicianRoleType: data.ClinicianRoleType || [1],
        Active: data.Active !== undefined ? data.Active : true,
      };

      // Merge current data with updates
      const updateData = {
        ...currentClinician,
        ...this.normalizeData(dataWithDefaults),
      };

      // Make API request
      const response = await axios.put(
        `${process.env.DOSESPOT_URL}/api/clinicians/${clinicianId}`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (response.data.Result?.ResultCode !== 'OK') {
        console.error('Clinician not updated: ', response.data);
        throw new BadRequestException(
          'Clinician not updated: ' + response.data.Result?.ResultDescription,
        );
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to update clinician in DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async createPatient(
    data: DosespotPatient,
    doseSpotClinicianId: string,
    pharmacyId: string,
  ): Promise<DosespotPatientResult> {
    const accessToken = await this.getToken(doseSpotClinicianId);

    const age = differenceInYears(new Date(), new Date(data.DateOfBirth));

    const { Weight, Height, ...baseData } = data;

    let patientData: DosespotPatient = baseData;
    if (age <= 19) {
      patientData = {
        ...baseData,
        Weight,
        WeightMetric: 1,
        Height,
        HeightMetric: 1,
      };
    }

    const normalizedData = this.normalizeData(patientData);

    try {
      const response = await axios.post<DosespotPatientResult>(
        `${process.env.DOSESPOT_URL}/api/patients`,
        normalizedData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (response.data.Result?.ResultCode !== 'OK') {
        console.error('Patient not created: ', response.data);
        throw new BadRequestException(
          'Patient not created: ' + response.data.Result?.ResultDescription,
        );
      }

      const patientId = response.data.Id;

      // Add patient to pharmacy
      await this.addPatientToPharmacy(
        patientId,
        pharmacyId,
        doseSpotClinicianId,
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to create patient in DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async getPatient(patientId: string, doctorId: string) {
    const accessToken = await this.getToken(doctorId);
    try {
      const response = await axios.get<{ Item: DosespotPatient }>(
        `${process.env.DOSESPOT_URL}/api/patients/${patientId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      return response.data.Item;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to get patient from DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async updatePatient(
    id: string,
    data: Partial<DosespotPatient>,
    doseSpotClinicianId: string,
  ) {
    const accessToken = await this.getToken(doseSpotClinicianId);

    try {
      const currentPatient = await this.getPatient(id, doseSpotClinicianId);
      const updatedFields = { ...currentPatient, ...this.normalizeData(data) };

      const updateResponse = await axios.put<DosespotPatientResult>(
        `${process.env.DOSESPOT_URL}/api/patients/${id}`,
        updatedFields,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (updateResponse.data.Result?.ResultCode !== 'OK') {
        console.error('Patient not updated: ', updateResponse.data);
        throw new BadRequestException(
          'Patient not updated: ' +
            updateResponse.data.Result?.ResultDescription,
        );
      }

      return updateResponse.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to update patient in DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async getPatientPharmacies(patientId: string, doctorId: string) {
    const accessToken = await this.getToken(doctorId);
    try {
      const response = await axios.get(
        `${process.env.DOSESPOT_URL}/api/patients/${patientId}/pharmacies`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      return response.data.Items;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to get patient details from DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async addPatientToPharmacy(
    patientId: string,
    pharmacyId: string,
    doseSpotClinicianId: string,
  ): Promise<void> {
    const accessToken = await this.getToken(doseSpotClinicianId);

    try {
      const response = await axios.post(
        `${process.env.DOSESPOT_URL}/api/patients/${patientId}/pharmacies`,
        { SetAsPrimary: true, PharmacyId: pharmacyId },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (response.data.Result?.ResultCode !== 'OK') {
        console.error('Failed to add patient to pharmacy: ', response.data);
        throw new BadRequestException(
          'Failed to add patient to pharmacy: ' +
            response.data.Result?.ResultDescription,
        );
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to add patient to pharmacy in DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async getClinician(clinicianId: string) {
    try {
      const accessToken = await this.getToken(process.env.DOSESPOT_ADMIN_ID);
      const response = await axios.get<{ Item: DosespotClinician }>(
        `${process.env.DOSESPOT_URL}/api/clinicians/${clinicianId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      return response.data.Item;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return false;
      }
      console.error('Error getting clinician:', error);
      return false;
    }
  }

  async getClinicianRegistrationStatus(
    clinicianId: string,
  ): Promise<DosespotRegistrationStatusType | null> {
    try {
      const accessToken = await this.getToken(process.env.DOSESPOT_ADMIN_ID);
      const response = await axios.get<DosespotRegistrationStatusResponse>(
        `${process.env.DOSESPOT_URL}/api/clinicians/${clinicianId}/registrationStatus`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (response.data.Result?.ResultCode !== 'OK') {
        console.error(
          'Failed to get clinician registration status: ',
          response.data,
        );
        return null;
      }

      return response.data.Item;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
      } else {
        console.error('Error:', error);
      }
      return null;
    }
  }

  async verifyClinicianExists(clinicianId: string) {
    return !!(await this.getClinician(clinicianId));
  }

  /**
   * Search for pharmacies by name
   * @param name Pharmacy name to search for (minimum 3 characters)
   * @param clinicianId DoseSpot clinician ID to use for authentication
   * @returns List of pharmacies matching the search criteria
   */
  async searchPharmacies(
    name: string,
    clinicianId: string,
  ): Promise<DosespotPharmacySearchResult> {
    if (!name || name.length < 3) {
      throw new BadRequestException(
        'Pharmacy name must be at least 3 characters long',
      );
    }

    try {
      const accessToken = await this.getToken(clinicianId);

      const response = await axios.get<DosespotPharmacySearchResult>(
        `${process.env.DOSESPOT_URL}/api/pharmacies/search`,
        {
          params: { name },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          },
        },
      );

      if (response.data.Result?.ResultCode !== 'OK') {
        console.error('Pharmacy search failed: ', response.data);
        throw new BadRequestException(
          'Pharmacy search failed: ' + response.data.Result?.ResultDescription,
        );
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios error:', error.response?.data || error.message);
        const extractedMessage = this.extractErrorMessage(error.response?.data);
        throw new BadRequestException(
          extractedMessage ||
            error.message ||
            'Failed to search pharmacies in DoseSpot',
        );
      } else {
        console.error('Error:', error);
        throw error; // Re-throw if it's already a BadRequestException
      }
    }
  }

  async getSSOUrl(
    dosespotPatientId: string,
    clinicianId: string,
  ): Promise<string> {
    try {
      const clinicId = process.env.DOSESPOT_CLINIC_ID;
      const clinicKey = process.env.DOSESPOT_CLINIC_KEY;
      const secret = this.generateRandomPhrase(32);

      const encryptedClinicId = this.getEncryptedClinicId(secret, clinicKey);
      const encryptedUserId = this.getEncryptedUserId(
        clinicianId,
        secret,
        clinicKey,
      );

      const ssoUrl = new URL(process.env.DOSESPOT_SINGLESIGNON_URL);
      ssoUrl.searchParams.append('SingleSignOnClinicId', clinicId);
      ssoUrl.searchParams.append('SingleSignOnUserId', clinicianId);
      ssoUrl.searchParams.append('SingleSignOnPhraseLength', '32');
      ssoUrl.searchParams.append(
        'SingleSignOnCode',
        encodeURIComponent(encryptedClinicId),
      );
      ssoUrl.searchParams.append(
        'SingleSignOnUserIdVerify',
        encodeURIComponent(encryptedUserId),
      );
      ssoUrl.searchParams.append('PatientId', dosespotPatientId);

      return ssoUrl.toString();
    } catch (error) {
      console.error('Error generating DoseSpot SSO URL:', error);
      throw new BadRequestException(
        error.message || 'Failed to generate DoseSpot SSO URL',
      );
    }
  }

  async getToken(userId: string): Promise<string> {
    if (this.tokenPromises[userId]) {
      return this.tokenPromises[userId];
    }

    this.tokenPromises[userId] = this.fetchToken(userId);

    try {
      return await this.tokenPromises[userId];
    } finally {
      delete this.tokenPromises[userId];
    }
  }

  private normalizeData(
    data: Partial<DosespotPatient>,
  ): Partial<DosespotPatient> {
    const normalizableFields = new Set([
      'FirstName',
      'MiddleName',
      'LastName',
      'Address1',
      'Address2',
      'State',
      'City',
      'ZipCode',
      'Email',
      'PrimaryPhone',
      'DateOfBirth',
    ]);
    const normalizedData = { ...data };
    for (const fieldName of Object.keys(data)) {
      if (
        normalizableFields.has(fieldName) &&
        typeof data[fieldName] === 'string'
      ) {
        normalizedData[fieldName] = normalizeText(data[fieldName]);
      }
    }
    return normalizedData;
  }

  private async fetchToken(userId: string): Promise<string> {
    if (
      !this.dosespotToken[userId] ||
      this.dosespotToken[userId].expires_in < Date.now()
    ) {
      const payload = {
        grant_type: 'password',
        client_id: process.env.DOSESPOT_CLINIC_ID,
        client_secret: process.env.DOSESPOT_CLINIC_KEY,
        username: userId,
        password: process.env.DOSESPOT_CLINIC_KEY,
        scope: 'api',
      };

      const postData = querystring.stringify(payload);

      const config = {
        headers: {
          'Subscription-Key': process.env.DOSESPOT_SUBSCRIPTION_KEY,
          'Content-Type': 'application/x-www-form-urlencoded',
          'Content-Length': postData.length,
        },
      };

      try {
        const response = await axios.post<DosespotToken>(
          `${process.env.DOSESPOT_URL}/connect/token`,
          postData,
          config,
        );

        if (!response.data.access_token) {
          throw new BadRequestException(
            'access_token not found: ' + JSON.stringify(response.data),
          );
        }

        this.dosespotToken[userId] = {
          access_token: response.data.access_token,
          expires_in: response.data.expires_in * 1000 + Date.now(),
        };
      } catch (error) {
        console.error('Error fetching DoseSpot token:', error);
        if (error instanceof BadRequestException) {
          throw error; // Re-throw if it's already a BadRequestException
        }
        throw new BadRequestException(
          error.message || 'Failed to fetch DoseSpot token',
        );
      }
    }

    return this.dosespotToken[userId].access_token;
  }

  private generateRandomPhrase(length: number): string {
    return randomBytes(length).toString('hex').slice(0, length);
  }

  private getEncryptedClinicId(secret: string, clinicKey: string): string {
    const combined = secret + clinicKey;
    let hashEncClinicId = createHash('sha512')
      .update(combined, 'utf8')
      .digest('base64');

    if (hashEncClinicId.endsWith('==')) {
      hashEncClinicId = hashEncClinicId.slice(0, -2);
    }

    return secret + hashEncClinicId;
  }

  private getEncryptedUserId(
    userId: string,
    secret: string,
    clinicKey: string,
  ): string {
    const secret22 = secret.substring(0, 22);
    const combined = userId + secret22 + clinicKey;

    let hashEncUserId = createHash('sha512')
      .update(combined, 'utf8')
      .digest('base64');

    if (hashEncUserId.endsWith('==')) {
      hashEncUserId = hashEncUserId.slice(0, -2);
    }

    return hashEncUserId;
  }
}
