export type DosespotPatient = {
  FirstName: string;
  LastName: string;
  DateOfBirth: Date;
  Gender: number; //1 --> MALE :  2--> FEMALE
  Weight?: number;
  Height?: number;
  Email: string;
  Address1: string;
  Address2?: string;
  City: string;
  State: string;
  ZipCode: string;
  PrimaryPhone: string;
  PrimaryPhoneType: number; // 2,
  Active: boolean;
  WeightMetric?: number; // 1 --> Lbs
  HeightMetric?: number; // 1 --> Inches
};

export type DosespotPatientResult = {
  Id: string;
  Result: {
    ResultCode: string;
    ResultDescription: string;
  };
};

export type DosespotPharmacy = {
  PharmacyId: number;
  StoreName: string;
  Address1: string;
  Address2: string | null;
  City: string;
  State: string;
  ZipCode: string;
  PrimaryPhone: string;
  PrimaryPhoneType: string;
  PrimaryFax: string;
  PhoneAdditional1: string | null;
  PhoneAdditionalType1: string;
  PhoneAdditional2: string | null;
  PhoneAdditionalType2: string;
  PhoneAdditional3: string | null;
  PhoneAdditionalType3: string;
  PharmacySpecialties: string | null;
  ServiceLevel: number;
  Latitude: number;
  Longitude: number;
  NCPDPID: string;
};

export type DosespotPharmacySearchResult = {
  Items: DosespotPharmacy[];
  Result: {
    ResultCode: string;
    ResultDescription: string;
  };
};

export enum DosespotRegistrationStatusType {
  Pending = 'Pending',
  RegistrationSuccess = 'RegistrationSuccess',
  RegistrationError = 'RegistrationError',
  IDPSuccess = 'IDPSuccess',
  IDPError = 'IDPError',
  TFAAcitvateInit = 'TFAAcitvateInit',
  TFAActivatedSuccess = 'TFAActivatedSuccess',
  TFAActivatedError = 'TFAActivatedError',
  TFADeactivateInit = 'TFADeactivateInit',
  TFADectivatedSuccess = 'TFADectivatedSuccess',
  TFADeactivatedError = 'TFADeactivatedError',
  IDPInitializeSuccess = 'IDPInitializeSuccess',
  NA = 'N/A',
}

export type DosespotRegistrationStatusResponse = {
  Item: DosespotRegistrationStatusType;
  Result: {
    ResultCode: string;
    ResultDescription: string;
  };
};

type ClinicInfo = {
  ClinicId: number;
  HasNewRx: boolean;
  HasRefills: boolean;
  HasRxChange: boolean;
  HasCancel: boolean;
  HasRxFill: boolean;
  HasEpcs: boolean;
  HasEpa: boolean;
};

export type DosespotClinician = {
  ClinicianId: number;
  Prefix: string | null;
  FirstName: string;
  MiddleName: string | null;
  LastName: string;
  Suffix: string | null;
  DateOfBirth: string; // ISO date string
  Email: string;
  Address1: string;
  Address2: string | null;
  City: string;
  State: string;
  ZipCode: string;
  PrimaryPhone: string;
  PrimaryPhoneType: string; // Was number in docs but actually string enum
  PrimaryFax: string;
  PhoneAdditional1: string | null;
  PhoneAdditionalType1: string; // Was number in docs but actually string enum
  PhoneAdditional2: string | null;
  PhoneAdditionalType2: string;
  PhoneAdditional3: string | null;
  PhoneAdditionalType3: string;
  DEANumbers: Array<any>; // Can be typed if needed
  DEANumber: string;
  NADEANumbers: Array<any>; // Can be typed if needed
  MedicalLicenseNumbers: Array<any>; // Can be typed if needed
  NPINumber: string;
  Roles: string[]; // Array of role names
  PDMPRoleType: string | null;
  SpecialtyTypeId: number | null;
  Confirmed: boolean;
  Active: boolean;
  AccountLocked: boolean;
  EpcsRequested: boolean;
  ClinicInfo: ClinicInfo[];
};
