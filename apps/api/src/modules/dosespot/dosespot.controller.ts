import * as process from 'node:process';
import { PreventImpersonatedEditGuard } from '@modules/auth/guards/prevent-impersonated-edit.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { MarkAskCompleteDto } from '@modules/dosespot/dto/mark-ask-complete.dto';
import {
  BadRequestException,
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Throttle } from '@nestjs/throttler';
import axios from 'axios';
import { Request } from 'express';

import {
  DosespotWebhookEvent,
  PrescriptionStatusChanges,
} from './types/webhook';
import { DosespotWebhookEventUseCase } from './use-cases/dosespot-webhook-event.use-case';
import { MarkAsCompleteSyncUseCase } from './use-cases/mark-as-complete-sync.use-case';

@Controller('dosespot')
export class DosespotController {
  constructor(
    private readonly dosespotWebhookEventUseCase: DosespotWebhookEventUseCase,
    private readonly markAsCompleteUseCase: MarkAsCompleteSyncUseCase,
  ) {}

  @Post('/')
  @Throttle({ default: { limit: 600, ttl: 60000 } })
  async webhook(@Body() payload: DosespotWebhookEvent) {
    // relay payload to a public endpoint for local testing
    if (process.env.ENVIRONMENT === 'staging') {
      for (const relay of ['willow-ramiro']) {
        axios
          .post(`https://${relay}.loca.lt/dosespot`, payload)
          .catch(() => {});
      }
    }

    switch (payload.EventType) {
      case 'PrescriberNotificationCounts':
        // call process prescription counts notification usecase here
        break;
      case 'PrescriptionResult':
        // call function to process prescription result here
        await this.dosespotWebhookEventUseCase.execute(
          payload as PrescriptionStatusChanges,
        );
        break;
      case 'MedicationStatusUpdate':
        // call function to process medication status update here
        break;

      case 'PharmacyStatusUpdate':
        // call function to process pharmacy status update here
        break;

      default:
        break;
    }
    return { message: 'webhook' };
  }

  // Hacky patch until we have a more robust Dosespot integration
  @Post('patient/complete')
  @UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
  @Roles(roles.Doctor)
  async markAsComplete(
    @Body() body: MarkAskCompleteDto,
    @Req() request: Request,
  ) {
    try {
      const doctorUserId: string = request.user['userId'];
      return await this.markAsCompleteUseCase.execute(body, doctorUserId);
    } catch (error) {
      return new BadRequestException(error.message);
    }
  }
}
