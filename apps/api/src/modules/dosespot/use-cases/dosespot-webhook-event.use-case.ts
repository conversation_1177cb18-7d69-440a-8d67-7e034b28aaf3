import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

import { PrescriptionStatusChanges } from '../types/webhook';

@Injectable()
export class DosespotWebhookEventUseCase {
  private prescriptionStatusTypes = {
    '1': 'Entered',
    '2': 'Printed',
    '3': 'Sending',
    '4': 'eRxSent',
    '5': 'FaxSent', //5 Deprecated
    '6': 'Error',
    '7': 'Deleted',
    '8': 'Requested',
    '9': 'Edited',
    '10': 'EpcsError ',
    '11': 'EpcsSigned ',
    '12': 'ReadyToSign ',
    '13': 'PharmacyVerified',
  };

  constructor(private readonly prismaService: PrismaService) {}

  async execute(_prescriptionUpdate: PrescriptionStatusChanges) {
    console.log('DosespotWebhookEventUseCase');
    // @todo implement
    return;
  }
}
