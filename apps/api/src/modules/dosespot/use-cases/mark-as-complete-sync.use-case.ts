import { PrismaService } from '@/modules/prisma/prisma.service';
import { MarkAskCompleteDto } from '@modules/dosespot/dto/mark-ask-complete.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MarkAsCompleteSyncUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(
    { stripeInvoiceId, patientId }: MarkAskCompleteDto,
    doctorUserId: string,
  ) {
    // get the patient and doctor record from the system
    const patient = await this.prismaService.patient.findUniqueOrThrow({
      where: {
        id: patientId,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        prescriptions: {
          where: {
            stripeInvoiceId,
          },
        },
      },
    });
    if (patient.doctor.user.id !== doctorUserId) {
      throw new Error('Unauthorized');
    }

    //update prescriptions
    await this.prismaService.prescription.updateMany({
      where: {
        stripeInvoiceId,
      },
      data: {
        doseSpotStatus: 'Requested',
      },
    });
  }
}
