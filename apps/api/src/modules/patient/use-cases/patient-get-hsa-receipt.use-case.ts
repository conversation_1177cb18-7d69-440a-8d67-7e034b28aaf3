import { PrismaService } from '@/modules/prisma/prisma.service';
import { StripeService } from '@/modules/stripe/service/stripe.service';
import { Injectable, NotFoundException, StreamableFile } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { endOfYear, getUnixTime, startOfYear } from 'date-fns';
import * as PDFDocument from 'pdfkit';

import { addSvgWillowLogo } from '../patient.generics';

@Injectable()
export class PatientGetHsaReceiptUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly stripeService: StripeService,
    private configService: ConfigService,
  ) {}

  async execute(patientId: string, year: string) {
    const receipt = await this.generateReceiptPdf(patientId, year);
    return new StreamableFile(receipt, {
      type: 'application/pdf',
      disposition: 'attachment; filename="hsa-receipt.pdf"',
    });
  }

  async generateReceiptPdf(patientId: string, year: string) {
    const resourcesPath = this.configService.get('resourcesPath');

    const receiptData = await this.getReceiptData(patientId, year);
    const doc = new PDFDocument();
    this.addFooter(doc);

    addSvgWillowLogo(doc, 72, 62, '#2F4C78');
    doc.restore();
    doc.text('STARTWILLOW.COM', 400, 72);
    doc.text('Tax ID: 93-3487527');
    doc.moveDown(3);
    doc.fontSize(24).text('Thank for your prescription', 72);
    doc.fontSize(12);

    doc.moveDown(2);
    this.boldText(doc, 'WILLOW MEDICAL');
    doc.text(`Dr. ${receiptData.doctor.fullName}`, 72, 215);
    doc.text(`License #: ${receiptData.doctor.license}`);
    doc.text(`NPI #:  ${receiptData.doctor.npi}`);
    doc.text('<EMAIL>');

    this.boldText(doc, 'PATIENT', 240, 197);
    doc.text(
      `${receiptData.patient.fullName} (${receiptData.patient.gender})`,
      240,
      215,
    );
    doc.text(receiptData.patient.birthDate);
    doc.text(receiptData.patient.phone);
    doc.text(receiptData.patient.fullAddress);

    doc.moveTo(72, 330).lineTo(560, 330).strokeColor('#F3F3F3').stroke();

    this.boldText(doc, 'DIAGNOSIS', 72, 350);
    doc.text('E66.9 - Obesity Unspecified', 240, 350);

    this.boldText(doc, 'TREATMENT', 72, 400);
    doc.text(
      'To reduce excess body weight and mantain weight reduction long term in adults with obesity',
      240,
      400,
    );

    doc.moveTo(72, 460).lineTo(560, 460).strokeColor('#F3F3F3').stroke();

    doc.x = doc.page.margins.left;
    doc.y = 490;

    //TABLE FOR THE RECEIPT's PRODUCTS
    const columnWidth =
      (doc.page.width - doc.page.margins.left - doc.page.margins.right) / 5;

    let lastDate = null;
    for (const product of receiptData.productsData) {
      let rowY = doc.y;
      const productTextHeight = doc.heightOfString(product.name, {
        width: columnWidth - 2,
      });

      const textWillExceedPageHeight =
        doc.y + productTextHeight >= doc.page.height - doc.page.margins.bottom;

      if (textWillExceedPageHeight) {
        doc.addPage();
        this.addFooter(doc);
        rowY = doc.page.margins.top;
      }
      const shouldPrintDate =
        lastDate == null ||
        lastDate != product.date ||
        textWillExceedPageHeight;

      doc.text(shouldPrintDate ? product.date : '', doc.x, rowY, {
        width: columnWidth - 2, //reduce the allowed width a little to create padding space between text of different columns
      });
      doc.x += columnWidth;

      lastDate = product.date;
      doc.text(product.name, doc.x, rowY, { width: columnWidth - 2 });
      doc.x += columnWidth;

      doc.text(product.pharmacy, doc.x, rowY, { width: columnWidth - 2 });
      doc.x += columnWidth;

      doc.text(product.refills, doc.x, rowY, { width: columnWidth - 2 });
      doc.x += columnWidth;

      doc.text(product.price, doc.x, rowY, { width: columnWidth - 2 });
      doc.x += columnWidth;

      doc.x = doc.page.margins.left;
      doc.y += productTextHeight;
    }

    this.boldText(doc, 'TOTAL', doc.page.margins.left + columnWidth * 3);
    doc.moveUp();
    this.boldText(
      doc,
      `$${receiptData.totalAmount}`,
      doc.page.margins.left + columnWidth * 4,
    );
    doc.x = doc.page.margins.left;
    doc.moveDown();
    doc
      .font(resourcesPath + '/Creattion.otf', 24)
      .text('Dr. ' + receiptData.doctor.fullName);
    doc.font('Helvetica', 12).text('Dr. ' + receiptData.doctor.fullName);
    doc.end();
    return doc;
  }

  private boldText(doc, text, ...args) {
    const docFontName = doc._font.name;
    doc
      .font(`${docFontName}-Bold`)
      .text(text, ...args)
      .font(docFontName);
  }

  /**
   * Adds a footer text to the bottom of the page.
   * NOTE: Call this method rigth after creating a new page because it reset the cursor position.
   */
  private addFooter(doc) {
    const oldBottomMargin = doc.page.margins.bottom;
    doc.page.margins.bottom = 0;
    doc
      .fillColor('gray')
      .text(
        'Items listed in report are elegible medical expenses under IRS code Section 213(d)',
        doc.page.margins.left,
        doc.page.height - 25,
      );
    doc.fillColor('black');
    doc.page.margins.bottom = oldBottomMargin;
    doc.x = doc.page.margins.left;
    doc.y = doc.page.margins.top;
  }

  async getReceiptData(patientId: string, year: string) {
    const patient = await this.prisma.patient.findFirst({
      where: { id: patientId },
      include: {
        user: true,
        shippingAddresses: {
          where: { default: true },
          include: { state: true },
        },
      },
    });

    const doctor = await this.prisma.doctor.findFirst({
      where: {
        id: patient.doctorId,
      },
      include: {
        user: true,
        prescribesIn: {
          where: {
            stateId: patient.stateId,
          },
        },
      },
    });

    if (!doctor) throw new Error('Patient must be accepted first');

    const doctorFullName = `${doctor.user.firstName} ${doctor.user.lastName}`;
    const doctorLicense = doctor.prescribesIn[0].licenseNumber || 'N/A';
    const doctorNpi = doctor.npiNumber || 'N/A';

    const patientFullName = `${patient.user.firstName} ${patient.user.lastName}`;
    const patientGender = patient.gender == 'male' ? 'M' : 'F';
    const patientBirthDate = new Date(patient.birthDate).toLocaleDateString();
    const patientPhone = patient.user.phone;
    const patientFullAddress = `${patient.shippingAddresses[0].address1}\n${patient.shippingAddresses[0].city}, ${patient.shippingAddresses[0].state.code}, ${patient.shippingAddresses[0].zip}`;

    // First, get paid prescriptions for the year from our database
    const paidPrescriptions = await this.prisma.prescription.findMany({
      where: {
        patientId: patient.id,
        status: 'paid',
        stripeInvoiceId: { not: null },
        createdAt: {
          gte: startOfYear(new Date(Number(year), 0)),
          lte: endOfYear(new Date(Number(year), 0)),
        },
      },
      include: {
        productPrice: {
          include: {
            product: true,
          },
        },
        pharmacy: true,
      },
      orderBy: { createdAt: 'asc' },
    });

    if (!paidPrescriptions.length) {
      throw new NotFoundException(
        'No paid prescriptions found for the given year',
      );
    }

    // Extract unique invoice IDs
    const invoiceIds = [
      ...new Set(
        paidPrescriptions.map((p) => p.stripeInvoiceId).filter(Boolean),
      ),
    ];

    // Fetch the specific invoices from Stripe
    const patientInvoices = await Promise.all(
      invoiceIds.map((invoiceId) =>
        this.stripeService.client().invoices.retrieve(invoiceId),
      ),
    );

    // Create a map of invoice IDs to prescriptions for efficient lookup
    const prescriptionsByInvoiceId = new Map();
    for (const prescription of paidPrescriptions) {
      if (!prescriptionsByInvoiceId.has(prescription.stripeInvoiceId)) {
        prescriptionsByInvoiceId.set(prescription.stripeInvoiceId, []);
      }
      prescriptionsByInvoiceId
        .get(prescription.stripeInvoiceId)
        .push(prescription);
    }

    let totalAmount = 0;
    const productsData = [];
    for (const invoice of patientInvoices) {
      const relatedPrescriptions =
        prescriptionsByInvoiceId.get(invoice.id) || [];

      invoice.lines.data.forEach((line, index) => {
        const amount = line.amount / 100;
        totalAmount += amount;

        // Use prescription data if available, otherwise fall back to line description
        const prescription = relatedPrescriptions[index];
        const name = prescription
          ? prescription.productPrice.name ||
            prescription.productPrice.product.name
          : line.description;
        const pharmacy = prescription ? prescription.pharmacy.name : 'N/A';

        productsData.push({
          date: new Date(invoice.created * 1000).toLocaleDateString(),
          price: `$${amount.toFixed(2)}`,
          name: name,
          pharmacy: pharmacy,
          refills: '0 Refills',
        });
      });
    }

    return {
      doctor: {
        fullName: doctorFullName,
        license: doctorLicense,
        npi: doctorNpi,
      },
      patient: {
        fullName: patientFullName,
        gender: patientGender,
        birthDate: patientBirthDate,
        phone: patientPhone,
        fullAddress: patientFullAddress,
      },
      productsData,
      totalAmount: totalAmount.toFixed(2),
    };
  }
}
