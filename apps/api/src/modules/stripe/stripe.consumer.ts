import { runInDbTransaction } from '@/helpers/transaction';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { Injectable, Logger } from '@nestjs/common';

import { CacheService } from '../cache/cache.service';
import { PrismaService } from '../prisma/prisma.service';
import { StripeInvoiceUpdatedQueueEvent } from '../shared/events/invoice-topic.definition';
import { ChargeUpdatedQueueEvent } from '../shared/events/stripe-topic.definition';
import { StripeService } from './service/stripe.service';
import { TrackChargeRefundedUseCase } from './use-cases/track-charge-refunded.use-case';
import { StripeTrackInvoicePaidUseCase } from './use-cases/track-invoice-paid.use-case';
import { StripeTrackInvoicePaymentFailedUseCase } from './use-cases/track-invoice-payment-failed.use-case';
import { StripeTrackInvoiceUncollectibleUseCase } from './use-cases/track-invoice-uncollectible.use-case';

@Injectable()
export class StripeConsumer {
  private readonly logger = new Logger(StripeConsumer.name);

  constructor(
    private readonly stripe: StripeService,
    private cacheService: CacheService,
    private readonly prismaService: PrismaService,
    private readonly stripeTrackInvoicePaidUseCase: StripeTrackInvoicePaidUseCase,
    private readonly stripeTrackInvoicePaymentFailedUseCase: StripeTrackInvoicePaymentFailedUseCase,
    private readonly stripeTrackInvoiceUncollectibleUseCase: StripeTrackInvoiceUncollectibleUseCase,
    private readonly stripeTrackchargeRefundedUseCase: TrackChargeRefundedUseCase,
  ) {}

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'stripe-create-invoice',
    maxRetries: 3,
    filter: ['create'],
  })
  async createInvoice({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'create') return;

    const { draftInvoice: draft } = payload;
    const trottleKey = `stripe:invoice-create-trottle:patientId_${payload.patientId}`;

    if (await this.cacheService.get(trottleKey)) {
      throw new Error(
        `Invoice creation throttled for draft ${draft.metadata.internalInvoiceId}`,
      );
    }
    await this.cacheService.set(trottleKey, 'true', 30);

    let invoiceId: string;
    const invoice = await this.stripe.listInvoices({
      customerId: draft.customerId,
      limit: 1,
      metadata: {
        internalInvoiceId: {
          equals: draft.metadata.internalInvoiceId,
        },
      },
    });

    if (invoice.data.length > 0) {
      this.logger.error(`INVOICE_ALREADY_EXISTS`, {
        patientId: payload.patientId,
        internalInvoiceId: draft.metadata.internalInvoiceId,
        foundInvoiceId: invoice.data[0].id,
        customerId: draft.customerId,
      });
      throw new Error('Invoice already exists for this draft');
    }

    try {
      const invoice = await this.stripe.createInvoice(draft.customerId, {
        description: draft.description,
        metadata: draft.metadata,
      });
      invoiceId = invoice.id;

      const prescriptionInvoiceItems: {
        prescriptionId: string;
        stripeInvoiceItemId: string;
        couponId?: string;
      }[] = [];
      for (const item of draft.items) {
        const invoiceItem = await this.stripe.addInvoiceItem({
          invoiceId: invoice.id,
          customerId: draft.customerId,
          item: item,
          couponId: item.couponId,
        });
        prescriptionInvoiceItems.push({
          prescriptionId: item.prescriptionId,
          stripeInvoiceItemId: invoiceItem.id,
        });
      }

      // apply coupons to the invoice
      const couponIds = draft.items
        .filter((item) => item.couponId)
        .map((item) => item.couponId);
      const invoiceCoupon = couponIds.length > 0 ? couponIds[0] : null;
      if (invoiceCoupon) {
        await this.stripe.applyCouponToInvoice(invoiceId, invoiceCoupon);
      }

      // finalize the invoice
      await runInDbTransaction(this.prismaService, async (prisma) => {
        for (const item of prescriptionInvoiceItems) {
          await prisma.prescription.update({
            where: { id: item.prescriptionId },
            data: {
              stripeInvoiceId: invoice.id,
              stripeInvoiceItemId: item.stripeInvoiceItemId,
              stripeCouponId: item.couponId,
              status: 'open',
            },
          });
        }

        // send the invoice to Stripe
        await this.stripe.attemptInvoiceCollect(invoiceId);
      });
    } catch (error) {
      this.logger.error('Error creating invoice', error);
      if (invoiceId) {
        try {
          await this.stripe.deletDraft(invoiceId);
          await this.cacheService.del(trottleKey);
        } catch (deleteError) {
          this.logger.error('Error voiding invoice after failure', deleteError);
        }
      }
      throw error;
    }
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'collect-new-invoice',
    filter: ['finalized'],
  })
  async attempCollectNewInvoice({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'finalized') return;
    this.logger.log('invoice finialized', payload.invoice.id);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-paid',
    filter: ['paid'],
  })
  async trackInvoicePaid({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'paid') return;

    await this.stripeTrackInvoicePaidUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-payment-failed',
    filter: ['payment_failed'],
  })
  async trackInvoicePaymentFailed({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'payment_failed') return;
    await this.stripeTrackInvoicePaymentFailedUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-uncollectible',
    filter: ['uncollectible', 'voided'],
  })
  async trackInvoiceUncollectible({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'uncollectible' && payload.event !== 'voided') return;
    await this.stripeTrackInvoiceUncollectibleUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'charge-updated',
    consumerGroup: 'track-charge-refunded',
    filter: ['refunded'],
  })
  async trackChargeRefunded({ payload }: ChargeUpdatedQueueEvent) {
    if (payload.event !== 'refunded') return;
    await this.stripeTrackchargeRefundedUseCase.execute(payload.charge);
  }
}
