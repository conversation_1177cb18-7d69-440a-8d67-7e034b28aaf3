import { DoctorSignInUseCase } from '@modules/doctor/use-cases/doctor-sign-in.use-case';
import { UserSignInDto } from '@modules/shared/dto/user-sign-in.dto';
import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  Post,
} from '@nestjs/common';
import { UnauthorizedException } from '@nestjs/common/exceptions/unauthorized.exception';
import { Throttle } from '@nestjs/throttler';

import { UserForgotPasswordDto } from '../shared/dto/user-forgot-password.dto';
import { UserRefreshTokenRequestDto } from '../shared/dto/user-refresh-token-request.dto';
import { UserResetPasswordWithTokenDto } from '../shared/dto/user-reset-password-with-token.dto';
import { UserForgotPasswordUseCase } from '../shared/use-cases/user-forgot-password-use.case';
import { UserResetPasswordWithTokenUseCase } from '../shared/use-cases/user-reset-password-with-token.use-case';
import { Doctor<PERSON>efreshTokenUseCase } from './use-cases/doctor-refresh-token-use.case';

@Controller('doctor')
export class DoctorAuthController {
  constructor(
    private readonly doctorSignInUseCase: DoctorSignInUseCase,
    private readonly userResetPasswordWithTokenUseCaseService: UserResetPasswordWithTokenUseCase,
    private readonly userForgotPasswordUseCaseService: UserForgotPasswordUseCase,
    private readonly doctorRefreshTokenUseCase: DoctorRefreshTokenUseCase,
  ) {}

  @Throttle({ default: { limit: 10, ttl: 60000 } })
  @Post('signIn')
  async signIn(@Body() body: UserSignInDto) {
    try {
      return await this.doctorSignInUseCase.execute(body);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      } else {
        throw new Error(e.message);
      }
    }
  }

  @Throttle({ default: { limit: 5, ttl: 300000 } })
  @Post('forgot-password')
  async forgotPassword(@Body() requestBody: UserForgotPasswordDto) {
    try {
      await this.userForgotPasswordUseCaseService.execute(requestBody);
    } catch {
      // Ignore errors - for security reasons we always return success
      // to prevent email enumeration attacks
    }

    // Always return success regardless of whether email exists
    return {
      status: 201,
      message: 'Password reset email sent successfully',
    };
  }

  @Throttle({ default: { limit: 5, ttl: 300000 } })
  @Post('reset-password')
  async resetPassword(@Body() requestBody: UserResetPasswordWithTokenDto) {
    const resetResult =
      await this.userResetPasswordWithTokenUseCaseService.execute(requestBody);
    if (resetResult.status === 200) {
      return resetResult;
    } else {
      throw new BadRequestException(resetResult.message);
    }
  }

  @Post('refresh')
  async refreshToken(@Body() requestBody: UserRefreshTokenRequestDto) {
    try {
      return await this.doctorRefreshTokenUseCase.execute(requestBody);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new UnauthorizedException('Invalid Refresh Token');
      } else {
        throw new Error(e.message);
      }
    }
  }
}
