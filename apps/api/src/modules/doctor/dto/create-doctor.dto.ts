import { <PERSON><PERSON><PERSON> } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class StateLicenseDto {
  @IsString()
  @IsNotEmpty()
  stateCode: string;

  @IsString()
  @IsOptional()
  licenseNumber?: string;
}

export class CreateDoctorDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsOptional()
  middleName?: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsString()
  @IsNotEmpty()
  dateOfBirth: string;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  states: string[]; // State codes

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StateLicenseDto)
  @IsOptional()
  stateLicenses?: StateLicenseDto[];

  @IsNumberString()
  @IsOptional()
  npiNumber?: string;

  @IsString()
  @IsOptional()
  primaryPhone?: string;

  @IsString()
  @IsOptional()
  primaryFax?: string;

  @IsString()
  @IsOptional()
  doseSpotClinicianId?: string;

  @IsString()
  @IsOptional()
  temporaryImageKey?: string;

  @IsString()
  @IsOptional()
  address1?: string;

  @IsString()
  @IsOptional()
  address2?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  @IsOptional()
  zip?: string;

  @IsEnum(DoctorRole)
  @IsOptional()
  role?: DoctorRole;
}
