import { states } from '@modules/shared/constants/states';
import { EmailTopLvlDomain } from '@modules/shared/validators/top-level-domain';
import { Transform } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEmail,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsStrongPassword,
  Min<PERSON>ength,
  Validate,
  ValidateIf,
} from 'class-validator';

export class DoctorSignUpDto {
  @IsNotEmpty()
  firstName: string;

  @IsOptional()
  middleName?: string;

  @IsNotEmpty()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  @Validate(EmailTopLvlDomain)
  email: string;

  @IsNotEmpty()
  @MinLength(10)
  @IsStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minNumbers: 1,
    minSymbols: 1,
    minUppercase: 1,
  })
  password: string;

  @IsNotEmpty()
  dateOfBirth: string;

  @IsArray()
  @IsIn(states, { each: true, message: 'You have selected invalid states' })
  @ArrayMinSize(1, { message: 'Must have at least one state' })
  states: string[];

  @IsNotEmpty()
  primaryPhone: string;

  @IsOptional()
  doseSpotClinicianId?: string;

  @ValidateIf((o) => !o.doseSpotClinicianId)
  @IsNotEmpty()
  npiNumber?: string;

  @ValidateIf((o) => !o.doseSpotClinicianId)
  @IsNotEmpty()
  primaryFax?: string;

  @ValidateIf((o) => !o.doseSpotClinicianId)
  @IsNotEmpty()
  address1?: string;

  @IsOptional()
  address2?: string;

  @ValidateIf((o) => !o.doseSpotClinicianId)
  @IsNotEmpty()
  city?: string;

  @ValidateIf((o) => !o.doseSpotClinicianId)
  @IsNotEmpty()
  @IsIn(states)
  state?: string;

  @ValidateIf((o) => !o.doseSpotClinicianId)
  @IsNotEmpty()
  zip?: string;
}
