import { join } from 'path';
import { runInDbTransaction } from '@/helpers/transaction';
import { Doctor<PERSON>ro<PERSON><PERSON> } from '@adapters/persistence/database/doctor.persistence';
import { CognitoService } from '@modules/auth/cognito.service';
import { CreateDoctorDto } from '@modules/doctor/dto/create-doctor.dto';
import { UpdateDoctorDto } from '@modules/doctor/dto/update-doctor.dto';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { DosespotRegistrationStatusType } from '@modules/dosespot/types/dosespot-patient';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import { SesService } from '@modules/shared/aws/ses/ses.service';
import { ValidateInputError } from '@modules/shared/errors/validate-input.error';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@modules/shared/events';
import { S3Service } from '@modules/shared/services/s3.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { bulkTransferStatus } from '@prisma/client';

import { SegmentIdentify, SegmentTrack } from '../shared/types/events';

@Injectable()
export class DoctorService {
  private bucket: string;
  private readonly assetsPath: string;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly prisma: PrismaService,
    private readonly dosespot: DosespotService,
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly cognitoService: CognitoService,
    private readonly sesService: SesService,
  ) {
    this.bucket = this.configService.get('AWS_S3_GENERAL_BUCKETNAME');
    this.assetsPath =
      process.env.ENVIRONMENT !== 'local' &&
      !process.argv.some((a) => a.includes('cli.ts'))
        ? '/app/apps/api/dist/assets'
        : join(process.cwd(), 'resources', 'assets');
  }

  async getData(userId: string): Promise<DoctorProfile> {
    const doctor = await this.prisma.doctor.findFirst({
      where: { user: { id: userId, type: 'doctor' } },
      include: { user: true, prescribesIn: true },
    });
    if (!doctor) throw new BadRequestException('Doctor not found');
    if (doctor.user.deletedAt)
      throw new BadRequestException('Doctor deactivated');

    return doctor;
  }

  async findAll(
    page = 1,
    limit = 10,
    search?: string,
    sortBy?: string,
    direction: 'asc' | 'desc' = 'asc',
    showInactive?: boolean,
  ) {
    const skip = (page - 1) * limit;
    const where: any = {};

    // Add search filter if provided
    if (search) {
      where.OR = [
        { user: { firstName: { contains: search, mode: 'insensitive' } } },
        { user: { lastName: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ];
    }

    // Filter by active status if needed
    if (showInactive === false) {
      where.user = {
        ...where.user,
        deletedAt: null,
      };
    }

    // Add sorting
    const orderBy: any = {};
    if (sortBy) {
      switch (sortBy) {
        case 'name':
          orderBy.user = { firstName: direction };
          break;
        case 'email':
          orderBy.user = { email: direction };
          break;
        case 'totalPatientCount':
          // We'll handle totalPatientCount sorting separately after counts are fetched
          break;
        default:
          orderBy.createdAt = direction;
      }
    } else {
      // When no explicit sort is provided, we'll use totalPatientCount sorting
      // which is handled after fetching counts
    }

    // For totalPatientCount sorting, we need to fetch all matching records first
    // Otherwise we use normal pagination
    const isSortingByPatientCount =
      sortBy === 'totalPatientCount' || !Object.keys(orderBy).length;

    let doctors;
    let total;

    if (isSortingByPatientCount) {
      // Get all matching doctors without pagination
      [doctors, total] = await Promise.all([
        this.prisma.doctor.findMany({
          where,
          include: {
            user: true,
            state: true,
            prescribesIn: { include: { state: true } },
          },
          // Still apply other sorting if available
          ...(Object.keys(orderBy).length ? { orderBy } : {}),
        }),
        this.prisma.doctor.count({ where }),
      ]);
    } else {
      // Normal paginated query with database sorting
      [doctors, total] = await Promise.all([
        this.prisma.doctor.findMany({
          where,
          skip,
          take: limit,
          include: {
            user: true,
            state: true,
            prescribesIn: { include: { state: true } },
          },
          orderBy,
        }),
        this.prisma.doctor.count({ where }),
      ]);
    }

    // Get doctor IDs to fetch patient counts
    const doctorIds = doctors.map((doctor) => doctor.id);

    // Get patient counts for each doctor in a single query
    const patientCounts = await this.prisma.patient.groupBy({
      by: ['doctorId'],
      where: {
        doctorId: {
          in: doctorIds,
        },
      },
      _count: {
        id: true,
      },
    });

    // Map the patient counts to doctors
    let doctorsWithPatientCounts = doctors.map((doctor) => {
      const countData = patientCounts.find(
        (count) => count.doctorId === doctor.id,
      );
      return {
        ...doctor,
        totalPatientCount: countData ? countData._count.id : 0,
      };
    });

    // Handle sorting by patient count if requested or no explicit sort was provided
    if (isSortingByPatientCount) {
      // Sort by patient count
      doctorsWithPatientCounts = doctorsWithPatientCounts.sort((a, b) => {
        // Default to desc when not specified for totalPatientCount
        const sortDirection = direction || 'desc';
        if (sortDirection === 'desc') {
          return (b.totalPatientCount || 0) - (a.totalPatientCount || 0);
        }
        return (a.totalPatientCount || 0) - (b.totalPatientCount || 0);
      });

      // Apply pagination after sorting
      const start = (page - 1) * limit;
      const end = start + limit;
      doctorsWithPatientCounts = doctorsWithPatientCounts.slice(start, end);
    }

    return {
      doctors: doctorsWithPatientCounts,
      pagination: {
        total,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
      },
    };
  }

  async findOne(id: string) {
    const doctor = await this.prisma.doctor.findUnique({
      where: { id },
      include: {
        user: true,
        state: true,
        prescribesIn: { include: { state: true } },
      },
    });

    if (!doctor) {
      return null;
    }

    // Check and update DoseSpot registration status if needed
    if (
      doctor.doseSpotClinicianId &&
      doctor.doseSpotRegistrationStatus !==
        DosespotRegistrationStatusType.IDPSuccess
    ) {
      // Get the current status from the API
      const apiStatus = await this.checkAndUpdateRegistrationStatus(id);

      // If we got a status from the API, use it in the response
      if (apiStatus) {
        doctor.doseSpotRegistrationStatus = apiStatus;
      }
    }

    // Get all states the doctor prescribes in
    const stateIds = doctor.prescribesIn.map((state) => state.stateId);

    // Get the count of patients grouped by state in a single query
    const patientsByState = await this.prisma.patient.groupBy({
      by: ['stateId'],
      where: { doctorId: doctor.id, stateId: { in: stateIds } },
      _count: { stateId: true },
    });

    // Map the counts to state information
    const statePatientCounts = doctor.prescribesIn.map((stateInfo) => {
      const stateCount = patientsByState.find(
        (patientState) => patientState.stateId === stateInfo.stateId,
      );

      return {
        stateId: stateInfo.stateId,
        stateName: stateInfo.state.name,
        stateCode: stateInfo.state.code,
        patientCount: stateCount ? stateCount._count.stateId : 0,
        enabled: stateInfo.state.enabled,
      };
    });

    // Get total count of patients for this doctor (one query)
    const totalPatientCount = await this.prisma.patient.count({
      where: {
        doctorId: doctor.id,
      },
    });

    // Get active bulk transfers for this doctor
    const activeBulkTransfers = await this.prisma.bulkTransfer.findMany({
      where: {
        doctorId: id,
        type: 'doctor',
        status: { in: ['pending', 'inProgress', 'reverting'] },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Return doctor information with patient counts and bulk transfers
    return {
      ...doctor,
      statePatientCounts,
      totalPatientCount,
      activeBulkTransfers,
    };
  }

  async update(id: string, data: UpdateDoctorDto) {
    // First get the doctor to update
    const doctor = await this.prisma.doctor.findUniqueOrThrow({
      where: { id },
      include: { user: true },
    });

    // Extract fields we want to handle differently
    const {
      doseSpotClinicianId,
      states,
      stateLicenses,
      password,
      temporaryImageKey,
      ...updateData
    } = data;

    // Initialize Prisma data object
    const prismaUpdateData: any = { ...updateData };

    // User data that might need to be updated
    const userUpdateData: any = {};

    // Extract user fields from update data
    if (updateData.firstName) userUpdateData.firstName = updateData.firstName;
    if (updateData.lastName) userUpdateData.lastName = updateData.lastName;
    if (updateData.email) userUpdateData.email = updateData.email;
    delete prismaUpdateData.firstName;
    delete prismaUpdateData.lastName;
    delete prismaUpdateData.email;

    // Handle password update if provided
    if (password) {
      // Update password in Cognito
      await this.cognitoService.overridePassword(doctor.user.email, password);
    }

    // Handle email update if provided
    if (updateData.email && updateData.email !== doctor.user.email) {
      // Check if email is already in use by another user
      const existingUser = await this.cognitoService.getUser(updateData.email);
      if (existingUser && existingUser['sub'] !== doctor.userId) {
        throw new ValidateInputError('Email is already in use by another user');
      }

      // Update email in Cognito
      await this.cognitoService.updateUserEmail(
        doctor.user.email,
        updateData.email,
      );
    }

    // Handle state update
    if (updateData.state) {
      const state = await this.prisma.state.findFirstOrThrow({
        where: { code: updateData.state },
        select: { id: true },
      });

      // Replace string with proper relation object
      prismaUpdateData.state = {
        connect: { id: state.id },
      };
      prismaUpdateData.stateId = undefined; // Remove the direct stateId reference
    }

    // Prepare state data for transaction if states are provided
    let selectedStates = [];
    let stateCreateData = [];

    if (states && states.length > 0) {
      selectedStates = await this.prisma.state.findMany({
        where: { code: { in: states } },
      });

      // Prepare create data for states with license information
      stateCreateData = selectedStates.map((s) => {
        const licenseInfo = stateLicenses?.find(
          (sl) => sl.stateCode === s.code,
        );
        return {
          doctorId: id,
          stateId: s.id,
          limit: 100,
          licenseNumber: licenseInfo?.licenseNumber || null,
        };
      });
    }

    // Prepare DoseSpot update if doctor has a DoseSpot clinician ID
    if (doctor.doseSpotClinicianId) {
      try {
        // Build DoseSpot update object
        const dosespotUpdate: any = {};

        if (updateData.firstName)
          dosespotUpdate.FirstName = updateData.firstName;
        if (updateData.middleName)
          dosespotUpdate.MiddleName = updateData.middleName;
        if (updateData.lastName) dosespotUpdate.LastName = updateData.lastName;
        if (updateData.email) dosespotUpdate.Email = updateData.email;
        if (updateData.address1) dosespotUpdate.Address1 = updateData.address1;
        if (updateData.address2) dosespotUpdate.Address2 = updateData.address2;
        if (updateData.city) dosespotUpdate.City = updateData.city;
        if (updateData.state) dosespotUpdate.State = updateData.state;
        if (updateData.zip) dosespotUpdate.ZipCode = updateData.zip;
        if (updateData.primaryPhone)
          dosespotUpdate.PrimaryPhone = updateData.primaryPhone;
        if (updateData.primaryFax)
          dosespotUpdate.PrimaryFax = updateData.primaryFax;
        if (updateData.dateOfBirth)
          dosespotUpdate.DateOfBirth = new Date(updateData.dateOfBirth);
        if (updateData.npiNumber)
          dosespotUpdate.NPINumber = updateData.npiNumber;

        // If we have any DoseSpot fields to update
        if (Object.keys(dosespotUpdate).length > 0) {
          // Clone the dosespot clinician data to preserve fields we don't update
          const clinician = await this.dosespot.getClinician(
            doctor.doseSpotClinicianId,
          );
          if (!clinician) {
            throw new BadRequestException(
              `DoseSpot clinician ${doctor.doseSpotClinicianId} not found`,
            );
          }

          // Update the clinician in DoseSpot
          await this.dosespot.updateClinician(
            doctor.doseSpotClinicianId,
            dosespotUpdate,
          );
        }
      } catch (error) {
        console.error('Error updating DoseSpot clinician:', error);
        throw new BadRequestException(error.message);
      }
    }

    // Handle profile image if provided
    if (temporaryImageKey) {
      try {
        // Add random characters to the filename to avoid Cloudflare cache issues
        const randomSuffix = Math.random().toString(36).substring(2, 10);
        const key = `doctors/${id}/profile-${randomSuffix}.jpg`;
        const moved = await this.s3Service.moveObject(
          this.bucket,
          temporaryImageKey,
          this.bucket,
          key,
        );

        if (moved) {
          prismaUpdateData.image = key;
        } else {
          console.error('Failed to move profile picture');
        }
      } catch (error) {
        console.error('Error updating profile picture:', error);
      }
    }

    // Execute transaction to update both user and doctor records
    return this.prisma.$transaction(async (tx) => {
      // Update user if needed
      if (Object.keys(userUpdateData).length > 0) {
        await tx.user.update({
          where: { id: doctor.userId },
          data: userUpdateData,
        });
      }

      // Handle states array for prescribesIn within the transaction
      if (states && states.length > 0) {
        // First delete existing prescribesIn relationships
        await tx.doctorsOnState.deleteMany({
          where: { doctorId: id },
        });

        // Then create new relationships
        if (stateCreateData.length > 0) {
          await tx.doctorsOnState.createMany({
            data: stateCreateData,
          });
        }
      }

      // Update doctor
      return tx.doctor.update({
        where: { id },
        data: prismaUpdateData,
        include: { user: true, prescribesIn: true },
      });
    });
  }

  async deactivate(id: string) {
    const now = new Date();

    const result = await this.prisma.$transaction(async (tx) => {
      // Get the doctor with user info
      const doctor = await tx.doctor.findUniqueOrThrow({
        where: { id },
        include: { user: true },
      });

      // Update the user with deletedAt timestamp
      await tx.user.update({
        where: { id: doctor.user.id },
        data: { deletedAt: now },
      });

      // Update the doctor to set active to false
      await tx.doctor.update({
        where: { id },
        data: { active: false },
      });

      return { success: true, doctor };
    });

    try {
      // Invalidate all tokens for this user
      if (result.doctor.user.email) {
        await this.cognitoService.signOutGlobally(result.doctor.user.email);
      }
    } catch (error) {
      console.error(`Failed to sign out doctor ${id} globally:`, error);
      // We don't throw here because the deactivation was successful
      // The token invalidation is a secondary action
    }

    return result;
  }

  async reactivate(id: string) {
    return this.prisma.$transaction(async (tx) => {
      // Get the doctor with user info
      const doctor = await tx.doctor.findUniqueOrThrow({
        where: { id },
        include: { user: true },
      });

      // Check if doctor is already active
      if (doctor.active === true) {
        throw new ValidateInputError('Doctor is already active');
      }

      // Update the user to clear deletedAt timestamp
      await tx.user.update({
        where: { id: doctor.user.id },
        data: { deletedAt: null },
      });

      // Update the doctor to set active to true
      await tx.doctor.update({
        where: { id },
        data: { active: true },
      });

      return {
        success: true,
        doctor: {
          ...doctor,
          active: true,
          user: { ...doctor.user, deletedAt: null },
        },
      };
    });
  }

  generateValidPassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    // Ensure we have at least one of each character type
    let password =
      lowercase[Math.floor(Math.random() * lowercase.length)] +
      uppercase[Math.floor(Math.random() * uppercase.length)] +
      numbers[Math.floor(Math.random() * numbers.length)] +
      symbols[Math.floor(Math.random() * symbols.length)];

    // Add more random characters to reach desired length
    const allChars = lowercase + uppercase + numbers + symbols;
    while (password.length < 12) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password characters
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  async create(data: CreateDoctorDto) {
    const {
      email,
      firstName,
      middleName,
      lastName,
      dateOfBirth,
      states,
      stateLicenses,
      npiNumber,
      primaryPhone,
      primaryFax,
      doseSpotClinicianId,
      temporaryImageKey,
      role = 'doctor',
      ...address
    } = data;

    // Generate a password if one wasn't provided
    const userPassword = this.generateValidPassword();

    const cognitoRole = 'Doctor';
    let cognitoUser: any = null;
    let clinicianId: string;
    let doctorId: string;

    try {
      // Validate state and get stateId
      const { id: stateId } = await this.prisma.state.findFirstOrThrow({
        where: { code: address.state },
        select: { id: true },
      });

      const selectedStates = await this.prisma.state.findMany({
        where: { code: { in: states } },
      });

      // DoseSpot clinician creation/validation
      try {
        const dosespotClinician = await this.dosespot.createClinician({
          FirstName: firstName,
          MiddleName: middleName,
          LastName: lastName,
          Email: email,
          DateOfBirth: new Date(dateOfBirth),
          Address1: address.address1,
          Address2: address.address2,
          City: address.city,
          State: address.state,
          ZipCode: address.zip,
          PrimaryPhone: primaryPhone,
          PrimaryPhoneType: 1,
          PrimaryFax: primaryFax,
          NPINumber: npiNumber,
          ClinicianRoleType: [1],
          Active: true,
          EPCSRequested: false,
        });
        clinicianId = dosespotClinician.Id.toString();
      } catch (dosespotError) {
        throw new BadRequestException(dosespotError.message);
      }

      // Create Cognito user
      cognitoUser = await this.cognitoService.signUp(
        email,
        userPassword,
        cognitoRole,
      );
      const userId = cognitoUser['sub'];

      // Create relationships with license information
      const prescribesInData = selectedStates.map((s) => {
        const licenseInfo = stateLicenses?.find(
          (sl) => sl.stateCode === s.code,
        );
        return {
          limit: 100,
          licenseNumber: licenseInfo?.licenseNumber || null,
          state: { connect: { id: s.id } },
        };
      });

      // Create user and doctor records
      const result = await this.prisma.user.create({
        data: {
          id: userId,
          email,
          firstName,
          lastName,
          type: 'doctor',
          doctor: {
            create: {
              address1: address.address1,
              address2: address.address2,
              city: address.city,
              stateId,
              zip: address.zip,
              // Store additional Dosespot fields
              middleName,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
              primaryPhone,
              primaryFax,
              npiNumber,
              doseSpotClinicianId: clinicianId,
              role,
              prescribesIn: {
                create: prescribesInData,
              },
            },
          },
        },
        include: { doctor: true },
      });

      doctorId = result.doctor.id;

      // Handle profile image
      // Add random characters to the filename to avoid Cloudflare cache issues
      const randomSuffix = Math.random().toString(36).substring(2, 10);
      const key = `doctors/${doctorId}/profile-${randomSuffix}.jpg`;
      try {
        if (temporaryImageKey) {
          // Move the temporary image to the permanent location
          const moved = await this.s3Service.moveObject(
            this.bucket,
            temporaryImageKey,
            this.bucket,
            key,
          );

          if (!moved) {
            throw new BadRequestException('Failed to move profile picture');
          }
        } else {
          // Upload default profile image if no temporary image was provided
          const localImagePath = join(this.assetsPath, 'doctor.jpg');
          await this.s3Service.uploadLocalFile(
            localImagePath,
            this.bucket,
            key,
          );
        }

        await this.prisma.doctor.update({
          where: { id: doctorId },
          data: { image: key },
        });
      } catch (uploadError) {
        console.error(
          `Failed to handle profile image for s3://${this.bucket}/${key}:`,
          uploadError,
        );
      }

      // Send welcome email with credentials
      try {
        const doctorUrl = this.configService.get('DOCTORS_URL');
        await this.sesService.sendEmail({
          to: email,
          bcc: [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ],
          subject: 'Your Willow Credentials',
          textBody: `Hi ${data.firstName},

Here are your credentials for logging in to the doctor dashboard in Willow:

url: ${doctorUrl}
email: ${data.email}
password: ${userPassword}

Let me know if you have any questions

Thanks`,
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't throw here, as the doctor was successfully created
      }

      return { id: doctorId, email, password: userPassword };
    } catch (e) {
      // If we created a Cognito user but something else failed, clean it up
      if (cognitoUser) {
        try {
          await this.cognitoService.deleteUser(email);
        } catch (deleteError) {
          console.error(
            'Failed to delete Cognito user during rollback:',
            deleteError,
          );
        }
      }

      console.error('Doctor creation failed:', e);
      throw e;
    }
  }

  async transferPatient(
    newDoctorId: string,
    patientId: string,
    reasonForReassignment?: string,
    bulkTransferId?: string,
    txClient?: PrismaTransactionalClient,
  ) {
    const patient = await this.prisma.patient.findUnique({
      where: { id: patientId },
      select: { doctorId: true },
    });
    if (!patient) {
      throw new BadRequestException('Patient not found');
    }

    const originalDoctor = await this.prisma.doctor.findUnique({
      where: { id: patient.doctorId },
      select: { id: true, user: true },
    });
    if (!originalDoctor)
      throw new BadRequestException('Original doctor not found');

    const newDoctor = await this.prisma.doctor.findUnique({
      where: { id: newDoctorId },
      select: { userId: true, user: true },
    });
    if (!newDoctor) throw new BadRequestException('Doctor not found');

    if (patient.doctorId === newDoctorId) {
      throw new BadRequestException(
        'Patient is already assigned to this doctor',
      );
    }

    return runInDbTransaction(txClient || this.prisma, async (tx) => {
      const currentAssignment = await tx.doctorAssignment.findFirst({
        where: { patientId },
        orderBy: { createdAt: 'desc' },
      });

      await tx.patient.update({
        where: { id: patientId },
        data: { doctorId: newDoctorId },
      });

      // Create a doctor assignment with optional bulk transfer ID
      await tx.doctorAssignment.create({
        data: {
          doctorId: newDoctorId,
          patientId,
          reasonForReassignment,
          previousAssignmentId: currentAssignment?.id,
          bulkTransferId: bulkTransferId,
        },
      });

      // If this is part of a bulk transfer, increment the completedJobs counter
      // and check if all jobs are completed
      if (bulkTransferId) {
        const bulkTransfer = await tx.bulkTransfer.findUnique({
          where: {
            id: bulkTransferId,
            type: 'doctor',
          },
          select: { status: true },
        });

        const updatedTransfer = await tx.bulkTransfer.update({
          where: {
            id: bulkTransferId,
            type: 'doctor',
          },
          data: { completedJobs: { increment: 1 } },
          select: { completedJobs: true, queuedJobs: true, status: true },
        });

        // Check if all jobs are completed and update status if needed
        if (updatedTransfer.completedJobs === updatedTransfer.queuedJobs) {
          // Determine the target status based on the current status
          let targetStatus: bulkTransferStatus = 'completed';

          // If this is a reversion (status is REVERTING), mark as REVERTED
          if (bulkTransfer.status === 'reverting') {
            targetStatus = 'reverted';
          }

          await tx.bulkTransfer.update({
            where: {
              id: bulkTransferId,
              type: 'doctor',
            },
            data: {
              status: targetStatus,
              completedAt: new Date(),
            },
          });
        }
      }

      const conversation = await tx.conversation.findFirst({
        where: { patientId },
        select: { id: true },
      });

      if (conversation) {
        if (!bulkTransferId) {
          await tx.conversation.update({
            where: { id: conversation.id },
            data: { lastMessageText: null },
          });
        }

        // For manual transfers (no bulkTransferId), set unread messages to 0
        // For bulk transfers, use the previous doctor's unread messages count
        let unreadMessagesCount = 0;

        if (bulkTransferId) {
          const previousDoctorWatcher = await tx.conversationWatcher.findFirst({
            where: {
              conversationId: conversation.id,
              userId: originalDoctor.user.id,
            },
            select: { unreadMessages: true },
          });

          unreadMessagesCount = previousDoctorWatcher?.unreadMessages || 0;
        }

        // Create or update the conversation watcher for the new doctor with the same unread messages count
        await tx.conversationWatcher.upsert({
          where: {
            conversationId_userId: {
              conversationId: conversation.id,
              userId: newDoctor.userId,
            },
          },
          create: {
            userId: newDoctor.userId,
            conversationId: conversation.id,
            unreadMessages: unreadMessagesCount,
          },
          update: {
            unreadMessages: unreadMessagesCount,
          },
        });
      }

      return { patientId, newDoctorId };
    });

    // Send identity call to Segment
    const identifyEvent: SegmentIdentify = {
      userId: patientId,
      traits: {
        doctorName: `${newDoctor.user.firstName} ${newDoctor.user.lastName}`,
        doctorId: newDoctorId,
      },
    };
    this.eventEmitter.emit(
      segmentIdentifyEvent.analyticIdentify,
      identifyEvent,
    );

    // Send track event to Segment
    this.eventEmitter.emit(segmentTrackEvents.patientReassigned.event, {
      event: segmentTrackEvents.patientReassigned.name,
      userId: patientId,
      properties: {
        originalDoctor: `${originalDoctor.user.firstName} ${originalDoctor.user.lastName}`,
        newDoctor: `${newDoctor.user.firstName} ${newDoctor.user.lastName}`,
        originalDoctorID: originalDoctor.id,
        newDoctorID: newDoctorId,
      },
    } satisfies SegmentTrack);
  }

  async checkAndUpdateRegistrationStatus(
    doctorId: string,
  ): Promise<string | null> {
    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
      select: {
        doseSpotClinicianId: true,
        doseSpotRegistrationStatus: true,
      },
    });

    if (!doctor || !doctor.doseSpotClinicianId) {
      return null;
    }

    // If the doctor already has IDPSuccess status, no need to check again
    if (
      doctor.doseSpotRegistrationStatus ===
      DosespotRegistrationStatusType.IDPSuccess
    ) {
      return doctor.doseSpotRegistrationStatus;
    }

    // Get the current registration status from DoseSpot
    const currentStatus = await this.dosespot.getClinicianRegistrationStatus(
      doctor.doseSpotClinicianId,
    );

    // If we couldn't get a status from the API, return the stored status
    if (!currentStatus) {
      return doctor.doseSpotRegistrationStatus;
    }

    // Only update the database if the status is IDPSuccess
    if (currentStatus === DosespotRegistrationStatusType.IDPSuccess) {
      await this.prisma.doctor.update({
        where: { id: doctorId },
        data: { doseSpotRegistrationStatus: currentStatus },
      });
    }

    // Always return the current status from the API
    return currentStatus;
  }
}
