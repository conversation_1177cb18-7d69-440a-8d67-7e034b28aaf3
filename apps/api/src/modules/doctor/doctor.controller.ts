import { PreventImpersonatedEditGuard } from '@modules/auth/guards/prevent-impersonated-edit.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { DoctorService } from '@modules/doctor/doctor.service';
import { PatientDto } from '@modules/doctor/dto/patient.dto';
import { RejectVerificationDto } from '@modules/doctor/dto/reject-verification.dto';
import { ShiftDatesDto } from '@modules/doctor/dto/shift-dates.dto';
import { DoctorAcceptPatientUseCase } from '@modules/doctor/use-cases/doctor-accept-patient.use-case';
import { DoctorRejectVerificationUseCase } from '@modules/doctor/use-cases/doctor-reject-verification.use-case';
import { DoctorVerifyPatientUseCase } from '@modules/doctor/use-cases/doctor-verify-patient.use-case';
import { GetDashboardForDoctorUseCase } from '@modules/doctor/use-cases/get-dashboard-for-doctor.use-case';
import { GetPatientForDoctorUseCase } from '@modules/doctor/use-cases/get-patient-for-doctor.use-case';
import { ShiftDatesUseCase } from '@modules/doctor/use-cases/shift-dates.use-case';
import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

import { UploadPhotoDto } from '../onboarding/dto/upload-photo.dto';
import { PatientUnsubscribeDto } from '../patient/dto/patient-unsubscribe.dto';
import { PatientCancelSubscriptionUseCase } from '../patient/use-cases/patient-cancel-subscription.use-case';
import { UpdatePatientIdInfoDto } from './dto/update-patient-id-info.dto';
import { DoctorGetByIdUseCase } from './use-cases/doctor-get-by-id.use-case';
import { DoctorIntakeSwimlaneUseCase } from './use-cases/doctor-intake-swimlane-use-case.service';
import { GetDoctorsForDoctorUseCase } from './use-cases/get-doctors-for-doctor.use-case';
import { GetPatientActivityLogUseCase } from './use-cases/get-patient-activity-log.use-case';
import { GetPatientPreSignedUrlUseCase } from './use-cases/get-patient-pre-signed-url.use-case';
import { GetPatientsForDoctorUseCase } from './use-cases/get-patients-for-doctor.use-case';
import { InpatientPendingDosespotPrescriptionUseCase } from './use-cases/inpatient-pending-dosespot-prescription.use-case';
import { PatientDosageUseCase } from './use-cases/patient-dosage.use-case';
import { PatientIntakeUseCase } from './use-cases/patient-intake-use-case.service';
import { UpdatePatientIdInfoUseCase } from './use-cases/update-patient-id-info.use-case';
import { UpdatePatientPhotoUseCase } from './use-cases/update-patient-photo.use-case';

@Controller('doctor')
@UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
@Roles(roles.Doctor)
export class DoctorController {
  constructor(
    private readonly doctorService: DoctorService,
    private readonly doctorAcceptPatientUseCase: DoctorAcceptPatientUseCase,
    private readonly doctorVerifyPatientUseCase: DoctorVerifyPatientUseCase,
    private readonly patientDosageUseCase: PatientDosageUseCase,
    private readonly doctorIntakeSwimlaneUseCase: DoctorIntakeSwimlaneUseCase,
    private readonly patientIntakeUseCase: PatientIntakeUseCase,
    private readonly inpatientPendingDosespotPrescriptionUseCase: InpatientPendingDosespotPrescriptionUseCase,
    private readonly getDoctorByIdUseCase: DoctorGetByIdUseCase,
    private readonly getPatientsForDoctorUseCase: GetPatientsForDoctorUseCase,
    private readonly getDoctorsForDoctorUseCase: GetDoctorsForDoctorUseCase,
    private readonly getPatientForDoctorUseCase: GetPatientForDoctorUseCase,
    private readonly doctorRejectVerificationUseCase: DoctorRejectVerificationUseCase,
    private readonly getDashboardForDoctorUseCase: GetDashboardForDoctorUseCase,
    private readonly patientCancelSubscriptionUseCase: PatientCancelSubscriptionUseCase,
    private readonly shiftDatesUseCase: ShiftDatesUseCase,
    private readonly updatePatientIdInfoUseCase: UpdatePatientIdInfoUseCase,
    private readonly getPatientPreSignedUrlUseCase: GetPatientPreSignedUrlUseCase,
    private readonly updatePatientPhotoUseCase: UpdatePatientPhotoUseCase,
    private readonly getPatientActivityLogUseCase: GetPatientActivityLogUseCase,
  ) {}

  @Get('dashboard')
  async getDoctorDashboard(@Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      return await this.getDashboardForDoctorUseCase.execute(doctor);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('status')
  async getDoctorStatus(@Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      return await this.doctorService.getData(userId);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('patients')
  async getDoctorPatients(
    @Req() request: Request,
    @Query('sortBy') sortBy: 'name' | 'lastMessageAt' = 'lastMessageAt',
    @Query('direction') direction: 'asc' | 'desc' = 'asc',
    @Query('page') pageString: string = '1',
    @Query('limit') limitString: string = '20',
    @Query('search') search?: string,
  ) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);

      const page = parseInt(pageString, 10);
      const limit = parseInt(limitString, 10);

      if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1) {
        throw new BadRequestException('Invalid page or limit parameters');
      }

      return await this.getPatientsForDoctorUseCase.execute(
        doctor,
        sortBy,
        direction,
        page,
        limit,
        search,
      );
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('patient/:patientId')
  async patient(
    @Param('patientId') patientId: string,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      return await this.getPatientForDoctorUseCase.execute(patientId, doctor);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('patient/:patientId/get-upload-url/:type')
  async getUploadUrl(
    @Param('patientId') patientId: string,
    @Param('type') type: string,
  ) {
    try {
      return await this.getPatientPreSignedUrlUseCase.execute(patientId, type);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('patient/:patientId/photo')
  @HttpCode(200)
  async photo(
    @Param('patientId') patientId: string,
    @Body() requestBody: UploadPhotoDto,
  ) {
    try {
      const { type } = requestBody;
      return await this.updatePatientPhotoUseCase.execute(patientId, type);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get(':doctorId/clinician')
  async getDoctorById(
    @Param('doctorId') doctorId: string,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      return await this.getDoctorByIdUseCase.execute(userId, doctorId);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }
  @Post('accept-patient')
  async acceptPatient(@Body() body: PatientDto, @Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      return await this.doctorAcceptPatientUseCase.execute(
        userId,
        body.patientId,
      );
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Post('verify-patient')
  async verifyPatient(@Body() body: PatientDto, @Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      return await this.doctorVerifyPatientUseCase.execute(
        userId,
        body.patientId,
      );
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Post('reject-verification')
  async rejectVerification(
    @Body() body: RejectVerificationDto,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      return await this.doctorRejectVerificationUseCase.execute(doctor, body);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('outpatient')
  async intakeSwinlane(@Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      return await this.doctorIntakeSwimlaneUseCase.execute(userId);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }
  @Get('patient/:patientId/dosage')
  async getPatientDosage(
    @Req() request: Request,
    @Param('patientId') patientId: string,
  ) {
    try {
      const userId: string = request.user['userId'];
      return await this.patientDosageUseCase.execute(patientId, userId);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      if (e.name === 'BadRequestException') {
        throw e;
      } else {
        throw new BadRequestException(e.message);
      }
    }
  }
  @Get('patient/:patientId/intake')
  async intake(@Req() request: Request, @Param('patientId') patientId: string) {
    try {
      const userId: string = request.user['userId'];
      return await this.patientIntakeUseCase.execute(userId, patientId);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      if (e.name === 'BadRequestException') {
        throw e;
      } else {
        throw new BadRequestException(e.message);
      }
    }
  }

  @Get('inpatient')
  async getPatientsWithPendingDosespotPrescriptions(@Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      return await this.inpatientPendingDosespotPrescriptionUseCase.execute(
        userId,
      );
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Post('patient/:patientId/cancel')
  async cancelPatientById(
    @Param('patientId') patientId: string,
    @Body() requestBody: PatientUnsubscribeDto,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      return await this.patientCancelSubscriptionUseCase.execute({
        ...requestBody,
        patientId,
        cancelledBy: {
          type: 'DOCTOR',
          userId: doctor.userId,
          id: doctor.id,
        },
      });
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Post('shift-dates')
  async shiftDates(
    @Body() requestBody: ShiftDatesDto,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      return await this.shiftDatesUseCase.execute(
        requestBody.patientId,
        doctor.id,
        requestBody.offsetInSeconds,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('transfer-patient')
  async transferPatient(
    @Body() requestBody: { patientId: string; newDoctorId: string },
    @Req() request: Request,
  ) {
    if (process.env.ENVIRONMENT === 'production') throw new NotFoundException();

    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      const { patientId, newDoctorId } = requestBody;
      const reasonForReassignment = `Transferred from Dr. ${doctor.user.firstName} ${doctor.user.lastName}`;
      await this.doctorService.transferPatient(
        newDoctorId,
        patientId,
        reasonForReassignment,
      );
      return {
        patientId,
        newDoctorId,
        reasonForReassignment,
      };
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('/patients/:patientId/update-id-info')
  async updatePatientIdInfo(
    @Param('patientId') patientId: string,
    @Body() requestBody: UpdatePatientIdInfoDto,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      const doctor = await this.doctorService.getData(userId);
      return await this.updatePatientIdInfoUseCase.execute({
        doctorId: doctor.id,
        patientId,
        data: requestBody,
      });
    } catch (e) {
      console.error(e);
      throw new BadRequestException(e.message);
    }
  }

  @Get('patients/:patientId/activity-log')
  async patientActivityLog(
    @Param('patientId') patientId: string,
    @Query('includes') includes?: string,
    @Query('excludes') excludes?: string,
  ) {
    try {
      return await this.getPatientActivityLogUseCase.execute(patientId, {
        includes,
        excludes,
      });
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('doctors')
  async getDoctorList(
    @Req() request: Request,
    @Query('direction') direction: 'asc' | 'desc' = 'asc',
    @Query('page') pageString: string = '1',
    @Query('limit') limitString: string = '20',
    @Query('search') search?: string,
  ) {
    try {
      const userId: string = request.user['userId'];
      const page = parseInt(pageString, 10);
      const limit = parseInt(limitString, 10);

      if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1) {
        throw new BadRequestException('Invalid page or limit parameters');
      }

      return await this.getDoctorsForDoctorUseCase.execute(
        userId,
        direction,
        page,
        limit,
        search,
      );
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }
}
