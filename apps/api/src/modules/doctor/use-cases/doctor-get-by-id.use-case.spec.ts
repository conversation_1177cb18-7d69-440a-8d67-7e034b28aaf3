import { Test, TestingModule } from '@nestjs/testing';

import { DoctorGetByIdUseCase } from './doctor-get-by-id.use-case';

describe('DoctorGetByIdUseCase', () => {
  let service: DoctorGetByIdUseCase;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DoctorGetByIdUseCase],
    }).compile();

    service = module.get<DoctorGetByIdUseCase>(DoctorGetByIdUseCase);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
