import { PrismaService } from '@/modules/prisma/prisma.service';
import { DoctorProfile } from '@adapters/persistence/database/doctor.persistence';
import { Injectable } from '@nestjs/common';
import { Patient } from '@prisma/client';

@Injectable()
export class GetPatientsForDoctorUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(
    doctor: DoctorPro<PERSON>le,
    _sortBy: 'name' | 'lastMessageAt' = 'lastMessageAt',
    _direction: 'asc' | 'desc' = 'asc',
    page: number = 1,
    limit: number = 20,
    search?: string,
  ) {
    const skip = (page - 1) * limit;

    const direction = _direction === 'asc' ? 'ASC' : 'DESC';
    const sortBy = _sortBy === 'name' ? 'firstName' : 'lastMessageAt';

    let similarity = 0.9;
    if (search?.length < 15) {
      similarity = 0.85;
    } else if (search?.length < 8) {
      similarity = 0.8;
    }

    const similarityQuery = `(similarity($5, LOWER(u."firstName"))  +  similarity($5, LOWER(u."lastName")) +  similarity($5, LOWER(u."email")) +  similarity($5, LOWER(u."id")))`;

    const query = `FROM "User" as u
    INNER JOIN "Patient" as p ON u.id = p."userId"
    LEFT JOIN "Conversation" as c ON c."patientId" = p.id
    LEFT JOIN "ConversationWatcher" as cw ON cw."conversationId" = c.id
    WHERE u."type" = 'patient' and u."deletedAt" IS NULL and p."doctorId" = $3 AND cw."userId" = $4
    ${search ? `AND ${similarityQuery} > ${similarity}` : ''}
    `;

    const dataQuery = `SELECT
    ${search ? `${similarityQuery} as score,` : '1 as score,'}
    p.id as "id", u.id as "userId",
    p.gender as "gender", p.status as "status",
    p."facePhoto" as "facePhoto",
    "firstName", "lastName", "email",
    c.id as "conversationId", c."updatedAt" as "lastMessageAt",
    cw."unreadMessages" as "unreadMessages"
    ${query}
    ORDER BY "score" DESC, "${sortBy}" ${direction} NULLS last
    LIMIT $1 OFFSET $2
    `;

    const countQuery = `SELECT
    count(*) as "count"
    ${query}
    `;

    const [patients, totalCount] = await Promise.all([
      this.prismaService
        .readReplica()
        .$queryRawUnsafe<
          {
            score: number;
            id: string;
            userId: string;
            status: Patient['status'];
            gender: Patient['gender'];
            facePhoto: Patient['facePhoto'];
            firstName: string;
            lastName: string;
            email: string;
            conversationId: string;
            lastMessageAt: Date;
            unreadMessages: number;
          }[]
        >(dataQuery, limit, skip, doctor.id, doctor.user.id, search ?? '')
        .then((result) =>
          result.map((patient) => {
            return {
              score: patient.score,
              id: patient.id,
              status: patient.status,
              gender: patient.gender,
              facePhoto: patient.facePhoto,
              user: {
                id: patient.userId,
                firstName: patient.firstName,
                lastName: patient.lastName,
                email: patient.email,
              },
              conversation: {
                id: patient.conversationId,
                updatedAt: patient.lastMessageAt,
                watcher: [
                  {
                    unreadMessages: patient.unreadMessages,
                  },
                ],
              },
            };
          }),
        ),
      this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(
          countQuery,
          0,
          0,
          doctor.id,
          doctor.user.id,
          search ?? '',
        )
        .then((result) => (result[0]?.count ? Number(result[0].count) : 0)),
    ]);

    return {
      patients,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPreviousPage: page > 1,
      },
    };
  }
}
