import { PrismaService } from '@modules/prisma/prisma.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ShiftDatesUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(patientId: string, doctorId: string, offsetInSeconds: number) {
    if (this.configService.get('ENVIRONMENT') === 'production') {
      throw new BadRequestException('Cannot shift dates in production');
    }

    const patient = await this.prisma.patient.findFirstOrThrow({
      where: { id: patientId },
    });

    if (!patient) {
      throw new BadRequestException('Patient not found');
    }

    if (patient.doctorId !== doctorId) {
      throw new BadRequestException('Patient does not belong to doctor');
    }

    await this.prisma.$transaction(
      async (tx) => {
        const offsetInterval = `${offsetInSeconds} seconds`;

        await tx.$executeRawUnsafe(
          `
            UPDATE "Conversation"
            SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END,
                "updatedAt" = CASE WHEN "updatedAt" IS NOT NULL THEN "updatedAt" - $1::interval END
            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "ConversationMessage"
            SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END
            WHERE "conversationId" IN (SELECT id
                                       FROM "Conversation"
                                       WHERE "patientId" = $2)
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "ConversationWatcher"
            SET "updatedAt" = CASE WHEN "updatedAt" IS NOT NULL THEN "updatedAt" - $1::interval END
            WHERE "conversationId" IN (SELECT id
                                       FROM "Conversation"
                                       WHERE "patientId" = $2)
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "DoctorAssignment"
            SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END
            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "Patient"
            SET "createdAt"  = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END,
                "updatedAt"  = CASE WHEN "updatedAt" IS NOT NULL THEN "updatedAt" - $1::interval END,
                "acceptedAt" = CASE WHEN "acceptedAt" IS NOT NULL THEN "acceptedAt" - $1::interval END,
                "verifiedAt" = CASE WHEN "verifiedAt" IS NOT NULL THEN "verifiedAt" - $1::interval END,
                "canceledAt" = CASE WHEN "canceledAt" IS NOT NULL THEN "canceledAt" - $1::interval END
            WHERE "id" = $2
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "PatientFollowUp"
            SET "createdAt"   = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END,
                "updatedAt"   = CASE WHEN "updatedAt" IS NOT NULL THEN "updatedAt" - $1::interval END,
                "scheduledAt" = CASE WHEN "scheduledAt" IS NOT NULL THEN "scheduledAt" - $1::interval END,
                "completedAt" = CASE WHEN "completedAt" IS NOT NULL THEN "completedAt" - $1::interval END
            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "PatientPaymentMethod"
            SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END
            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "PatientShippingAddress"
            SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END
            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        // Add Treatment table update
        await tx.$executeRawUnsafe(
          `
            WITH updated_state AS (SELECT id,
                                          COALESCE(state, '{}'::jsonb)              as state,
                                          COALESCE(state -> 'context', '{}'::jsonb) as context
                                   FROM "Treatment"
                                   WHERE "patientId" = $2)
            UPDATE "Treatment" t
            SET "createdAt"          = CASE
                                         WHEN t."createdAt" IS NOT NULL THEN t."createdAt" - $1::interval
                                         ELSE NULL END,
                "updatedAt"          = CASE
                                         WHEN t."updatedAt" IS NOT NULL THEN t."updatedAt" - $1::interval
                                         ELSE NULL END,
                "nextEventIn"        = CASE
                                         WHEN t."nextEventIn" IS NOT NULL THEN t."nextEventIn" - $1::interval
                                         ELSE NULL END,
                "nextNotificationIn" = CASE
                                         WHEN t."nextNotificationIn" IS NOT NULL THEN t."nextNotificationIn" - $1::interval
                                         ELSE NULL END,
                "completedAt"        = CASE
                                         WHEN t."completedAt" IS NOT NULL THEN t."completedAt" - $1::interval
                                         ELSE NULL END,
                "failedAt"           = CASE
                                         WHEN t."failedAt" IS NOT NULL THEN t."failedAt" - $1::interval
                                         ELSE NULL END,
                "uncollectibleAt"    = CASE
                                         WHEN t."uncollectibleAt" IS NOT NULL THEN t."uncollectibleAt" - $1::interval
                                         ELSE NULL END,
                "cancelledAt"        = CASE
                                         WHEN t."cancelledAt" IS NOT NULL THEN t."cancelledAt" - $1::interval
                                         ELSE NULL END,
                "deletedAt"          = CASE
                                         WHEN t."deletedAt" IS NOT NULL THEN t."deletedAt" - $1::interval
                                         ELSE NULL END,
                "state"              = jsonb_build_object(
                                         'context', jsonb_build_object(
                                                      'delayUntil', CASE
                                                                      WHEN us.context ->> 'delayUntil' IS NOT NULL
                                                                        THEN to_jsonb(
                                                                        to_char(
                                                                          (us.context ->> 'delayUntil')::timestamptz -
                                                                          $1::interval,
                                                                          'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                      ELSE us.context -> 'delayUntil'
                      END,
                                                      'nextRefillDate', CASE
                                                                          WHEN us.context ->> 'nextRefillDate' IS NOT NULL
                                                                            THEN to_jsonb(
                                                                            to_char(
                                                                              (us.context ->> 'nextRefillDate')::timestamptz -
                                                                              $1::interval,
                                                                              'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                          ELSE us.context -> 'nextRefillDate'
                                                        END,
                                                      'endOfLastRefillDate', CASE
                                                                               WHEN us.context ->> 'endOfLastRefillDate' IS NOT NULL
                                                                                 THEN to_jsonb(
                                                                                 to_char(
                                                                                   (us.context ->> 'endOfLastRefillDate')::timestamptz -
                                                                                   $1::interval,
                                                                                   'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                               ELSE us.context -> 'endOfLastRefillDate'
                                                        END,
                                                      'nextEventIn', CASE
                                                                       WHEN us.context ->> 'nextEventIn' IS NOT NULL
                                                                         THEN to_jsonb(
                                                                         to_char(
                                                                           (us.context ->> 'nextEventIn')::timestamptz -
                                                                           $1::interval,
                                                                           'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                       ELSE us.context -> 'nextEventIn'
                                                        END,
                                                      'nextNotificationIn', CASE
                                                                              WHEN us.context ->> 'nextNotificationIn' IS NOT NULL
                                                                                THEN to_jsonb(
                                                                                to_char(
                                                                                  (us.context ->> 'nextNotificationIn')::timestamptz -
                                                                                  $1::interval,
                                                                                  'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                              ELSE us.context -> 'nextNotificationIn'
                                                        END,
                                                      'lastChargedAt', CASE
                                                                         WHEN us.context ->> 'lastChargedAt' IS NOT NULL
                                                                           THEN to_jsonb(
                                                                           to_char(
                                                                             (us.context ->> 'lastChargedAt')::timestamptz -
                                                                             $1::interval,
                                                                             'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                         ELSE us.context -> 'lastChargedAt'
                                                        END,
                                                      'inProgressSince', CASE
                                                                           WHEN us.context ->> 'inProgressSince' IS NOT NULL
                                                                             THEN to_jsonb(
                                                                             to_char(
                                                                               (us.context ->> 'inProgressSince')::timestamptz -
                                                                               $1::interval,
                                                                               'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                           ELSE us.context -> 'inProgressSince'
                                                        END,
                                                      'pausedAt', CASE
                                                                    WHEN us.context ->> 'pausedAt' IS NOT NULL
                                                                      THEN to_jsonb(
                                                                      to_char((us.context ->> 'pausedAt')::timestamptz -
                                                                              $1::interval,
                                                                              'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                    ELSE us.context -> 'pausedAt'
                                                        END,
                                                      'failedAt', CASE
                                                                    WHEN us.context ->> 'failedAt' IS NOT NULL
                                                                      THEN to_jsonb(
                                                                      to_char((us.context ->> 'failedAt')::timestamptz -
                                                                              $1::interval,
                                                                              'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                    ELSE us.context -> 'failedAt'
                                                        END,
                                                      'completedAt', CASE
                                                                       WHEN us.context ->> 'completedAt' IS NOT NULL
                                                                         THEN to_jsonb(
                                                                         to_char(
                                                                           (us.context ->> 'completedAt')::timestamptz -
                                                                           $1::interval,
                                                                           'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                       ELSE us.context -> 'completedAt'
                                                        END,
                                                      'deletedAt', CASE
                                                                     WHEN us.context ->> 'deletedAt' IS NOT NULL
                                                                       THEN to_jsonb(
                                                                       to_char(
                                                                         (us.context ->> 'deletedAt')::timestamptz -
                                                                         $1::interval, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                     ELSE us.context -> 'deletedAt'
                                                        END,
                                                      'uncollectibleAt', CASE
                                                                           WHEN us.context ->> 'uncollectibleAt' IS NOT NULL
                                                                             THEN to_jsonb(
                                                                             to_char(
                                                                               (us.context ->> 'uncollectibleAt')::timestamptz -
                                                                               $1::interval,
                                                                               'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))
                                                                           ELSE us.context -> 'uncollectibleAt'
                                                        END
                                                    ) || (us.context - array [
                    'delayUntil', 'nextRefillDate', 'endOfLastRefillDate', 'nextEventIn',
                    'nextNotificationIn', 'lastChargedAt', 'inProgressSince', 'pausedAt',
                    'failedAt', 'completedAt', 'deletedAt', 'uncollectibleAt'
                    ])
                                       ) || (us.state - 'context')
            FROM updated_state us
            WHERE t."patientId" = $2
              AND t.id = us.id
          `,
          offsetInterval,
          patientId,
        );

        // Add Prescription table update
        await tx.$executeRawUnsafe(
          `
            UPDATE "Prescription"
            SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval ELSE NULL END,
                "updatedAt" = CASE WHEN "updatedAt" IS NOT NULL THEN "updatedAt" - $1::interval ELSE NULL END
            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        await tx.$executeRawUnsafe(
          `
            UPDATE "Subscription"
            SET "createdAt"             = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END,
                "updatedAt"             = CASE WHEN "updatedAt" IS NOT NULL THEN "updatedAt" - $1::interval END,
                "startDate"             = CASE WHEN "startDate" IS NOT NULL THEN "startDate" - $1::interval END,
                "endDate"               = CASE WHEN "endDate" IS NOT NULL THEN "endDate" - $1::interval END,
                "lastChargeSuccessDate" = CASE
                                            WHEN "lastChargeSuccessDate" IS NOT NULL
                                              THEN "lastChargeSuccessDate" - $1::interval END

            WHERE "patientId" = $2
          `,
          offsetInterval,
          patientId,
        );

        const patient = await tx.patient.findUnique({
          where: { id: patientId },
          select: { userId: true },
        });

        if (patient) {
          await tx.$executeRawUnsafe(
            `
              UPDATE "User"
              SET "createdAt" = CASE WHEN "createdAt" IS NOT NULL THEN "createdAt" - $1::interval END,
                  "deletedAt" = CASE WHEN "deletedAt" IS NOT NULL THEN "deletedAt" - $1::interval END
              WHERE "id" = $2
            `,
            offsetInterval,
            patient.userId,
          );
        }
      },
      { timeout: 10_00_000, maxWait: 1_000_000 },
    );
  }
}
