import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { S3Service } from '@modules/shared/services/s3.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class UpdatePatientPhotoUseCase {
  private bucket: string;
  constructor(
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly auditService: AuditService,
  ) {
    this.bucket = this.configService.get<string>(
      'AWS_S3_PATIENT_PHOTOS_BUCKETNAME',
    );
  }

  async execute(patientId: string, type: 'id-photo' | 'face-photo') {
    const key = `${patientId}/photo-${type}.jpg`;

    // Verify file exists in s3
    const exists = await this.s3Service.objectExists(this.bucket, key);
    if (!exists) {
      throw new Error(`${type} photo not found`);
    }

    const data = type === 'face-photo' ? { facePhoto: key } : { idPhoto: key };

    // Update patient record
    const updatedPatient = await this.prismaService.patient.update({
      where: { id: patientId },
      data,
    });

    void this.auditService.append({
      patientId,
      action: 'PATIENT_ID_PHOTO_UPDATED',
      actorType: 'DOCTOR',
      actorId: updatedPatient.doctorId,
      resourceType: 'PATIENT',
      resourceId: patientId,
      details: {
        type,
        file: key,
      },
    });

    return {
      patientId,
      ...data,
    };
  }
}
