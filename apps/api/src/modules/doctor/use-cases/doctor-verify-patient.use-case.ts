import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentTrack } from '@/modules/shared/types/events';
import { segmentTrackEvents } from '@modules/shared/events';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class DoctorVerifyPatientUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly auditService: AuditService,
  ) {}

  async execute(doctorUserId: string, patientId: string) {
    // get patient data
    const patient = await this.prismaService.patient.findFirstOrThrow({
      where: { id: patientId },
      include: {
        user: true,
      },
    });

    // check if patient has a doctor
    if (patient.verificationStatus === 'verified') {
      throw new BadRequestException('Patient already been verified');
    }

    // validate doctor serving states with patient state
    const doctor = await this.prismaService.doctor.findFirstOrThrow({
      where: { userId: doctorUserId },
      include: {
        user: true,
        prescribesIn: {
          where: {
            stateId: patient.stateId,
          },
        },
      },
    });

    // verification
    await this.prismaService.$transaction(async (prisma) => {
      // update patient verifiedbyuser with doctor id
      await prisma.patient.update({
        where: { id: patientId },
        data: {
          verifiedByUser: doctor.id,
          verifiedAt: new Date(),
          updatedAt: new Date(),
          verificationStatus: 'verified',
        },
      });
      void this.auditService.append({
        patientId: patient.id,
        action: 'PATIENT_IDENTITY_ACCEPTED',
        actorType: 'DOCTOR',
        actorId: doctor.id,
        resourceType: 'PATIENT',
        resourceId: patient.id,
        details: {},
      });
    });

    const patientIdentityAccepted: SegmentTrack = {
      event: segmentTrackEvents.identityAccepted.name,
      userId: patient.id,
      properties: {
        doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
        doctorID: doctor.id,
        patientName: `${patient.user.firstName} ${patient.user.lastName}`,
        patientID: patient.id,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.identityAccepted.event,
      patientIdentityAccepted,
    );
  }
}
