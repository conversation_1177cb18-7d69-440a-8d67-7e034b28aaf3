import { S3Service } from '@modules/shared/services/s3.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetPatientPreSignedUrlUseCase {
  constructor(private readonly s3Service: S3Service) {}

  async execute(patientId: string, type: string) {
    if (!['id-photo', 'face-photo'].includes(type)) {
      throw new Error('Invalid type');
    }
    const key = `${patientId}/photo-${type}.jpg`;
    const bucket = process.env.AWS_S3_PATIENT_PHOTOS_BUCKETNAME;
    return this.s3Service.generatePUTPreSignedURL(bucket, key, 600);
  }
}
