import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, NotFoundException } from '@nestjs/common';

@Injectable()
export class PatientDosageUseCase {
  constructor(private readonly prisma: PrismaService) {}

  async execute(patientId: string, doctorId: string) {
    await this.validateDoctor(doctorId);
    const patient = await this.getPatientWithDesiredTreatments(patientId);
    const desiredTreatmentsProducts = this.extractDesiredTreatments(patient);

    // Get pharmacies first to ensure we maintain their priority order
    const pharmacies = await this.getEnabledPharmacies(patient.stateId);

    // Now use the ordered pharmacy IDs to maintain the same order in productsByPharmacy
    const productsByPharmacy = await this.getAllActiveProductsByPharmacy(
      patient.stateId,
      pharmacies.map((p) => p.id),
    );

    return { desiredTreatmentsProducts, productsByPharmacy, pharmacies };
  }

  private async validateDoctor(doctorId: string) {
    const doctor = await this.prisma.doctor.findUnique({
      where: { userId: doctorId },
    });
    if (!doctor) throw new NotFoundException('Doctor not found');
  }

  private async getPatientWithDesiredTreatments(patientId: string) {
    const patient = await this.prisma.patient.findUnique({
      where: { id: patientId },
      include: {
        desiredTreatments: {
          include: {
            product: {
              include: {
                productPrice: {
                  where: { active: true },
                  select: { id: true, name: true, metadata: true },
                },
              },
            },
          },
        },
      },
    });
    if (!patient) throw new NotFoundException('Patient not found');
    return patient;
  }

  private extractDesiredTreatments(patient) {
    return patient.desiredTreatments.map((dt) => ({
      ...dt.product,
      vials: dt.vials,
    }));
  }

  private async getAllActiveProductsByPharmacy(
    stateId: string,
    orderedPharmacyIds: string[] = [],
  ) {
    const pharmaciesInState = await this.prisma.pharmacyOnState.findMany({
      where: {
        stateId,
        pharmacyId: {
          in: orderedPharmacyIds.length > 0 ? orderedPharmacyIds : undefined,
        },
      },
      select: { pharmacyId: true },
    });

    const pharmacyIds =
      orderedPharmacyIds.length > 0
        ? orderedPharmacyIds
        : pharmaciesInState.map((p) => p.pharmacyId);

    const products = await this.prisma.product.findMany({
      where: {
        active: true,
        pharmacyId: { in: pharmacyIds },
      },
      select: {
        id: true,
        name: true,
        metadata: true,
        pharmacyId: true,
        productPrice: {
          where: { active: true },
          select: {
            id: true,
            name: true,
            unit_amount: true,
            metadata: true,
          },
        },
      },
    });

    // Create a result object that preserves pharmacy order
    const result = {};

    // Initialize entries for all pharmacies in the correct order
    for (const pharmacyId of pharmacyIds) {
      result[pharmacyId] = [];
    }

    // Fill in the products
    for (const product of products) {
      product.name = `${product.metadata['label']}, ${product.metadata['form']}`;
      result[product.pharmacyId].push(product);
    }

    return result;
  }

  private async getEnabledPharmacies(stateId: string) {
    // First get all enabled pharmacies in the state
    const pharmacies = await this.prisma.pharmacy.findMany({
      where: {
        enabled: true,
        PharmacyOnState: {
          some: {
            stateId,
          },
        },
      },
      select: {
        id: true,
        name: true,
        color: true,
        regularPriority: true,
        usingGLP1Priority: true,
        Product: {
          where: {
            active: true,
          },
          select: {
            id: true,
          },
        },
      },
      orderBy: { regularPriority: 'desc' },
    });

    // Filter out pharmacies that don't have any active products
    return pharmacies
      .filter((pharmacy) => pharmacy.Product?.length > 0)
      .map(({ Product, ...pharmacy }) => pharmacy); // Remove the Product array from the result
  }
}
