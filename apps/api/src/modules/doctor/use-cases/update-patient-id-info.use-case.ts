import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { PatientService } from '@/modules/patient/patient.service';
import { BadRequestException, Injectable } from '@nestjs/common';

import { UpdatePatientIdInfoDto } from '../dto/update-patient-id-info.dto';

@Injectable()
export class UpdatePatientIdInfoUseCase {
  constructor(
    private readonly patientService: PatientService,
    private readonly patientPersistence: PatientPersistence,
  ) {}

  async execute({
    doctorId,
    patientId,
    data,
  }: {
    doctorId: string;
    patientId: string;
    data: UpdatePatientIdInfoDto;
  }) {
    const patientProfile =
      await this.patientPersistence.getDashboardProfile(patientId);

    if (patientProfile.doctor.id !== doctorId) {
      throw new Error('Patient not managed by this doctor');
    }

    if (
      patientProfile.verificationStatus === 'verified' &&
      (data.birthDate || data.firstName || data.lastName)
    ) {
      throw new BadRequestException(
        'You cannot update your name or birth date after verification',
      );
    }

    return this.patientService.updateProfile(patientProfile, data, {
      updatedBy: {
        type: 'DOCTOR',
        id: doctorId,
      },
    });
  }
}
