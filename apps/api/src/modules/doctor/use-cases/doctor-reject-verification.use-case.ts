import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { DoctorProfile } from '@adapters/persistence/database/doctor.persistence';
import { RejectVerificationDto } from '@modules/doctor/dto/reject-verification.dto';
import { ForbiddenError } from '@modules/shared/errors/forbidden.error';
import { segmentTrackEvents } from '@modules/shared/events';
import { SegmentTrack } from '@modules/shared/types/events';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

const reasonMap = {
  noIdUploaded: { item: 'ID Photo', reason: 'No ID Uploaded' },
  noSelfieUploaded: { item: 'Selfie Photo', reason: 'No Selfie Uploaded' },
  '2selfiesUploaded': { item: 'Selfie Photo', reason: '2 Selfies Uploaded' },
  '2idsUploaded': { item: 'ID Photo', reason: '2 IDs Uploaded' },
  nameIncorrect: { item: 'ID Photo', reason: 'Name Incorrect' },
  dobIncorrect: { item: 'ID Photo', reason: 'DOB Incorrect' },
  pictureOfIdIsTooBlurry: {
    item: 'ID Photo',
    reason: 'Picture of ID is too Blurry',
  },
  selfieIsTooBlurry: { item: 'Selfie Photo', reason: 'Selfie is too Blurry' },
  other: { item: 'ID Photo', reason: 'Other' },
};

@Injectable()
export class DoctorRejectVerificationUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly auditService: AuditService,
  ) {}

  async execute(doctor: DoctorProfile, data: RejectVerificationDto) {
    const patient = await this.prismaService.patient.findFirstOrThrow({
      where: {
        id: data.patientId,
      },
      include: {
        user: true,
      },
    });
    if (!patient.doctorId || patient.doctorId !== doctor.id) {
      throw new ForbiddenError('Patient not assigned to doctor');
    }

    await this.prismaService.patient.update({
      where: {
        id: patient.id,
      },
      data: {
        verificationStatus: 'rejected',
        rejectedStatus: data.rejectedStatus,
        rejectedReason: data.rejectedReason,
        rejectedAt: new Date(),
      },
    });

    void this.auditService.append({
      patientId: patient.id,
      action: 'PATIENT_REJECTED',
      actorType: 'DOCTOR',
      actorId: doctor.id,
      resourceType: 'PATIENT',
      resourceId: patient.id,
      details: {
        status: data.rejectedStatus,
        reason: data.rejectedReason,
      },
    });

    const patientIdentityRejectedEvent: SegmentTrack = {
      event: segmentTrackEvents.identityRejected.name,
      userId: patient.id,
      properties: {
        doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
        doctorID: doctor.id,
        patientName: `${patient.user.firstName} ${patient.user.lastName}`,
        patientID: patient.id,
        item: reasonMap[data.rejectedStatus].item,
        reason: reasonMap[data.rejectedStatus].reason,
        note: data.rejectedReason,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.identityRejected.event,
      patientIdentityRejectedEvent,
    );
  }
}
