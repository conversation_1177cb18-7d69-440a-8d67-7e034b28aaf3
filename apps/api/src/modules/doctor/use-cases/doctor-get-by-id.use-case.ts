import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class DoctorGetByIdUseCase {
  constructor(private readonly prismaService: PrismaService) {}
  async execute(userId: string, doctorId: string) {
    // return await this.prismaService.doctor.findFirstOrThrow({
    //   where: {
    //     id: doctorId,
    //   },
    //   select: {
    //     user: true,
    //   },
    // });
    return await this.prismaService.doctor.findFirstOrThrow({
      where: {
        id: doctorId,
      },
      include: {
        user: true,
      },
    });
  }
}
