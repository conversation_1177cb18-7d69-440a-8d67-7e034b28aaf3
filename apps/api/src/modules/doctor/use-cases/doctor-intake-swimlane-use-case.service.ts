import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { patientStatus } from '@prisma/client';

@Injectable()
export class DoctorIntakeSwimlaneUseCase {
  constructor(private readonly prismaService: PrismaService) {}
  async execute(userId: string) {
    // validate if user is a doctor and get the covered state for this doctor
    const doctor = await this.prismaService.doctor.findFirstOrThrow({
      where: { userId: userId },
      include: {
        prescribesIn: true,
      },
    });
    const coveredStates = doctor.prescribesIn.map((state) => state.stateId);
    // get all patient with completed onboarding and no doctor assigned and in the same state as doctor
    const patients = await this.prismaService.patient.findMany({
      where: {
        doctorId: null,
        status: patientStatus.onboardingCompleted,
        stateId: {
          in: coveredStates,
        },
      },
      include: {
        user: true,
        state: true,
        shippingAddresses: {
          where: {
            default: true,
          },
          select: {
            city: true,
            address1: true,
            zip: true,
            default: true,
          },
        },
      },
    });
    // modify the response so that only the default shipping is available at the root object level instead of an array
    // patients.forEach((patient) => {
    //   patient.city = patient.shippingAddresses[0].city;
    //   patient.address1 = patient.shippingAddresses[0].address1;
    //   patient.zip = patient.shippingAddresses[0].zip;
    //   delete patient.shippingAddresses;
    // });

    return patients;
  }
}
