import { S3Service } from '@modules/shared/services/s3.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DoctorGetProfilePictureUrlUseCase {
  private bucket: string;

  constructor(
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
  ) {
    this.bucket = this.configService.get('AWS_S3_GENERAL_BUCKETNAME');
  }

  async execute() {
    // Generate a temporary key for the upload
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 15);
    const key = `temp/doctors/${timestamp}-${randomString}.jpg`;

    const preSignedURL = await this.s3Service.generatePUTPreSignedURL(
      this.bucket,
      key,
      600,
    );

    return {
      key,
      preSignedURL,
    };
  }
}
