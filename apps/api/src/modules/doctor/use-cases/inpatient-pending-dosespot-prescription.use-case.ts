import { PrismaService } from '@/modules/prisma/prisma.service';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { patientStatus } from '@prisma/client';

type InvoiceGroup = {
  updatedAt: Date;
  treatments: Array<{
    id: string;
    productPrice: {
      productName: string;
      priceName: string;
    };
    treatmentId: string;
    prescriptionId: string;
    form: string;
    vials: number;
    prescriptionDate: Date;
  }>;
  patientId: string;
  birthDate: Date;
  user: { firstName: string; lastName: string };
  state: { code: string; name: string };
  pharmacy: { name: string; metadata: any; color?: string };
  sso: string;
};

@Injectable()
export class InpatientPendingDosespotPrescriptionUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly dosespotService: DosespotService,
  ) {}

  async execute(userId: string): Promise<InvoiceGroup[]> {
    const doctor = await this.prismaService.doctor.findUnique({
      where: { userId },
      include: { prescribesIn: true },
    });

    if (!doctor) {
      throw new NotFoundException('Doctor not found');
    }

    const treatments = await this.prismaService.treatment.findMany({
      where: {
        status: 'inProgress.waitingForPrescription',
        patient: {
          doctorId: doctor.id,
          status: {
            in: [
              patientStatus.pendingPrescription,
              patientStatus.nonActivePrescription,
              patientStatus.activePrescription,
            ],
          },
        },
      },
      select: {
        id: true,
        updatedAt: true,
        state: true,
        vials: true,
        transferredTo: true,
        patient: {
          select: {
            id: true,
            birthDate: true,
            doseSpotPatientId: true,
            user: { select: { firstName: true, lastName: true } },
            state: { select: { name: true, code: true } },
            pharmacy: { select: { name: true, metadata: true, color: true } },
          },
        },
        prescription: {
          where: { status: 'paid' },
          select: {
            id: true,
            createdAt: true,
            stripeInvoiceId: true,
            productPrice: {
              select: {
                id: true,
                name: true,
                unit_amount: true,
                metadata: true,
                product: { select: { name: true, metadata: true } },
                equivalenceGroupId: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    const invoiceGroups: { [key: string]: InvoiceGroup } = {};

    for (const treatment of treatments) {
      const prescription = treatment.prescription[0]; // Get the last paid prescription
      if (!prescription || !prescription.stripeInvoiceId) continue;

      // Check if treatment has been transferred to another pharmacy
      let displayProductPrice = prescription.productPrice;
      let displayPharmacy = treatment.patient.pharmacy;

      if (
        treatment.transferredTo &&
        prescription.productPrice.equivalenceGroupId
      ) {
        // Fetch the transferred treatment to get the new pharmacy
        const transferredTreatment =
          await this.prismaService.treatment.findUnique({
            where: { id: treatment.transferredTo },
            select: {
              patient: {
                select: {
                  pharmacy: {
                    select: {
                      id: true,
                      name: true,
                      metadata: true,
                      color: true,
                    },
                  },
                },
              },
            },
          });

        if (transferredTreatment?.patient?.pharmacy) {
          // Find the equivalent product price for the new pharmacy
          const equivalentProductPrice =
            await this.prismaService.productPrice.findFirst({
              where: {
                equivalenceGroupId:
                  prescription.productPrice.equivalenceGroupId,
                product: {
                  pharmacyId: transferredTreatment.patient.pharmacy.id,
                },
              },
              select: {
                id: true,
                name: true,
                unit_amount: true,
                metadata: true,
                equivalenceGroupId: true,
                product: { select: { name: true, metadata: true } },
              },
            });

          if (equivalentProductPrice) {
            // Use the equivalent product price and the new pharmacy for display
            displayProductPrice = equivalentProductPrice;
            displayPharmacy = transferredTreatment.patient.pharmacy;
          }
        }
      }

      const sso =
        treatment.patient.doseSpotPatientId && doctor.doseSpotClinicianId
          ? await this.dosespotService.getSSOUrl(
              treatment.patient.doseSpotPatientId,
              doctor.doseSpotClinicianId,
            )
          : '';

      if (!invoiceGroups[prescription.stripeInvoiceId]) {
        invoiceGroups[prescription.stripeInvoiceId] = {
          updatedAt: treatment.updatedAt,
          treatments: [],
          patientId: treatment.patient.id,
          birthDate: treatment.patient.birthDate,
          user: treatment.patient.user,
          state: treatment.patient.state,
          pharmacy: {
            name: displayPharmacy.name,
            metadata: displayPharmacy.metadata,
            color: displayPharmacy.color,
          },
          sso,
        };
      }

      invoiceGroups[prescription.stripeInvoiceId].treatments.push({
        id: treatment.id,
        productPrice: {
          productName: displayProductPrice.product.metadata['label'],
          priceName: displayProductPrice.metadata['dosageLabel'],
        },
        treatmentId: treatment.id,
        prescriptionId: prescription.id,
        form: treatment.state['context'].activeProduct.form,
        vials: treatment.vials,
        prescriptionDate: prescription.createdAt,
      });
    }

    return Object.values(invoiceGroups).sort(
      (a, b) => a.updatedAt.getTime() - b.updatedAt.getTime(),
    );
  }
}
