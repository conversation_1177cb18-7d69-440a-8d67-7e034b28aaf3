import { PrismaService } from '@/modules/prisma/prisma.service';
import { CognitoService } from '@modules/auth/cognito.service';
import { DoctorService } from '@modules/doctor/doctor.service';
import {
  DoctorSignInOutput,
  UserSignInInput,
} from '@modules/shared/types/user/user.types';
import { Injectable } from '@nestjs/common';

@Injectable()
export class DoctorSignInUseCase {
  constructor(
    private readonly cognitoService: CognitoService,
    private readonly prisma: PrismaService,
    private readonly doctorService: DoctorService,
  ) {}

  async execute(data: UserSignInInput): Promise<DoctorSignInOutput> {
    const { email, password } = data;
    const role = 'Doctor';

    const tokens = await this.cognitoService.signIn(email, password, role);

    // validate the role
    const roles = tokens.getAccessToken().payload['cognito:groups'];
    if (!roles.includes(role)) {
      throw new Error(`Invalid role ${role} for user ${email}`);
    }

    // validate the user in ddbb
    const user = await this.prisma.user.findFirst({
      where: { email, type: 'doctor', deletedAt: null },
    });
    if (!user) {
      const err = new Error(`Doctor with email ${email} not found`);
      err.name = 'NotAuthorizedException';
      throw err;
    }

    const doctor = await this.doctorService.getData(user.id);

    return {
      accessToken: tokens.getAccessToken().getJwtToken(),
      refreshToken: tokens.getRefreshToken().getToken(),
      role,
      doctor,
    };
  }
}
