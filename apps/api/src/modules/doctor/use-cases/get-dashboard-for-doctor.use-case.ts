import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  DoctorPersist<PERSON>,
  DoctorProfile,
} from '@adapters/persistence/database/doctor.persistence';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetDashboardForDoctorUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly patientPersistence: PatientPersistence,
    private readonly doctorPersistence: DoctorPersistence,
  ) {}

  async execute(doctor: DoctorProfile) {
    //--
    const availablePatients =
      await this.patientPersistence.getAvailablePatientsForDoctor(doctor);

    const { pendingApprovalPatients, revalidatedPatients } =
      await this.patientPersistence.getPendingApprovalPatientsForDoctor(doctor);

    const lastMessages = await this.doctorPersistence.getLastMessages(
      doctor.user.id,
    );

    return {
      availablePatients,
      pendingApprovalPatients,
      revalidatedPatients,
      lastMessages,
    };
  }
}
