import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetDoctorsForDoctorUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(
    userId: string,
    _direction: 'asc' | 'desc' = 'asc',
    page: number = 1,
    limit: number = 20,
    search?: string,
  ) {
    const skip = (page - 1) * limit;

    const direction = _direction === 'asc' ? 'ASC' : 'DESC';
    const sortBy = 'firstName';

    let similarity = 0.9;
    if (search?.length < 15) {
      similarity = 0.85;
    } else if (search?.length < 8) {
      similarity = 0.8;
    }

    const similarityQuery = `(similarity($3, LOWER(u."firstName"))  +  similarity($3, LOWER(u."lastName")) +  similarity($3, LOWER(u."email")) +  similarity($3, LOWER(u."id")))`;

    const query = `FROM "User" as u
    INNER JOIN "Doctor" as d ON u.id = d."userId"
    WHERE u."type" = 'doctor' and u."deletedAt" IS NULL AND u."id" <> $4
    ${search ? `AND ${similarityQuery} > ${similarity}` : ''}
    `;

    const dataQuery = `SELECT
    ${search ? `${similarityQuery} as score,` : '1 as score,'}
    d.id as "id", u.id as "userId",
    "firstName", "lastName", "email"
    ${query}
    ORDER BY "score" DESC, "${sortBy}" ${direction} NULLS last
    LIMIT $1 OFFSET $2
    `;

    const countQuery = `SELECT
    count(*) as "count"
    ${query}
    `;

    const [doctors, totalCount] = await Promise.all([
      this.prismaService
        .readReplica()
        .$queryRawUnsafe<
          {
            score: number;
            id: string;
            userId: string;
            firstName: string;
            lastName: string;
            email: string;
          }[]
        >(dataQuery, limit, skip, search ?? '', userId)
        .then((result) =>
          result.map((doctor) => {
            return {
              score: doctor.score,
              id: doctor.id,
              user: {
                id: doctor.userId,
                firstName: doctor.firstName,
                lastName: doctor.lastName,
                email: doctor.email,
              },
            };
          }),
        ),
      this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(
          countQuery,
          0,
          0,
          search ?? '',
          userId,
        )
        .then((result) => (result[0]?.count ? Number(result[0].count) : 0)),
    ]);

    return {
      doctors,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPreviousPage: page > 1,
      },
    };
  }
}
