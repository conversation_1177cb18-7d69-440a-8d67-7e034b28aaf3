import { PrismaService } from '@/modules/prisma/prisma.service';
import { QuestionnairService } from '@/modules/shared/questionnaire/questionnaire.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class PatientIntakeUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly questionairService: QuestionnairService,
  ) {}
  async execute(doctorId: string, patientId: string) {
    // Check if this is a doctor
    const doctor = await this.prismaService.doctor.findFirstOrThrow({
      where: { userId: doctorId },
    });
    // make sure the doctor is the one who is assigned to this patient
    const patient = await this.prismaService.patient.findFirstOrThrow({
      where: { id: patientId, doctorId: doctor.id },
      include: {
        user: true,
        state: true,
        questionnaire: true,
      },
    });
    // get the keys of the questionaire and map it to the questions
    const questions = await this.questionairService.getQuestionnaire(
      `v${patient.questionnaire.version}`,
    );
    const questionnaire = patient.onboardingState['context']
      .questionnaire as Record<string, any>;
    const record = Object.keys(questionnaire).map((key) => {
      let answer = questionnaire[key];
      switch (key) {
        case 'height': {
          const ft = Math.floor(answer / 12);
          const inches = answer % 12;
          answer = `${ft}ft ${inches}in`;
          break;
        }
        case 'weight':
        case 'desiredWeight':
          answer = `${answer}lbs`;
          break;
      }
      if (Array.isArray(answer)) {
        answer = answer.join(', ');
      }
      return {
        question: questions[key],
        answer,
      };
    });
    const version = {
      version: patient.questionnaire.version,
      name: patient.questionnaire.name,
    };
    return { ...version, record };
  }
}
