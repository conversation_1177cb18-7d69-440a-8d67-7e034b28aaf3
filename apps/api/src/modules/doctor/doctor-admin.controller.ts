import { RequireCapabilities } from '@modules/auth/decorators/require-capabilities.decorator';
import { CapabilityGuard } from '@modules/auth/guards/capability.guard';
import { roles } from '@modules/auth/types/roles';
import { DoctorService } from '@modules/doctor/doctor.service';
import { CreateDoctorDto } from '@modules/doctor/dto/create-doctor.dto';
import { DoctorGetProfilePictureUrlUseCase } from '@modules/doctor/use-cases/doctor-get-profile-picture-url.use-case';
import {
  TransferPatientsDto,
  UpdateBulkTransferDto,
} from '@modules/patient/dto/transfer-patients.dto';
import { PatientTransferService } from '@modules/patient/patient-transfer.service';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { Capability } from '@willow/auth';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { UserForgotPasswordDto } from '../shared/dto/user-forgot-password.dto';
import { UserForgotPasswordUseCase } from '../shared/use-cases/user-forgot-password-use.case';
import { UpdateDoctorDto } from './dto/update-doctor.dto';

@Controller('admin/doctors')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles(roles.Admin)
export class DoctorAdminController {
  constructor(
    private readonly doctorService: DoctorService,
    private readonly doctorGetProfilePictureUrlUseCase: DoctorGetProfilePictureUrlUseCase,
    private readonly patientTransferService: PatientTransferService,
    private readonly userForgotPasswordUseCaseService: UserForgotPasswordUseCase,
  ) {}

  @Get()
  @RequireCapabilities(Capability.VIEW_DOCTORS)
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('direction') direction?: 'asc' | 'desc',
    @Query('showInactive') showInactive?: boolean,
  ) {
    return this.doctorService.findAll(
      page,
      limit,
      search,
      sortBy,
      direction,
      showInactive,
    );
  }

  @Get('get-upload-url')
  @RequireCapabilities(Capability.CREATE_DOCTORS, Capability.EDIT_DOCTORS)
  async getUploadUrl() {
    try {
      return await this.doctorGetProfilePictureUrlUseCase.execute();
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get(':id')
  @RequireCapabilities(Capability.VIEW_DOCTORS)
  findOne(@Param('id') id: string) {
    return this.doctorService.findOne(id);
  }

  @Post()
  @RequireCapabilities(Capability.CREATE_DOCTORS)
  create(@Body() createDoctorDto: CreateDoctorDto) {
    return this.doctorService.create(createDoctorDto);
  }

  @Patch(':id')
  @RequireCapabilities(Capability.EDIT_DOCTORS)
  update(@Param('id') id: string, @Body() updateDoctorDto: UpdateDoctorDto) {
    return this.doctorService.update(id, updateDoctorDto);
  }

  @Delete(':id')
  @RequireCapabilities(Capability.DEACTIVATE_DOCTORS)
  deactivate(@Param('id') id: string) {
    return this.doctorService.deactivate(id);
  }

  @Patch(':id/reactivate')
  @RequireCapabilities(Capability.REACTIVATE_DOCTORS)
  reactivate(@Param('id') id: string) {
    return this.doctorService.reactivate(id);
  }

  @Post('/transfer-patients')
  @RequireCapabilities(Capability.EDIT_DOCTORS)
  async transferPatients(@Body() requestBody: TransferPatientsDto) {
    try {
      // For scheduled transfers, just create the bulk transfer record and let the cron job handle it
      const isScheduledTransfer =
        requestBody.transferAt && new Date(requestBody.transferAt) > new Date();

      // Create the bulk transfer record (and perform immediately if not scheduled)
      const result =
        await this.patientTransferService.transferToDoctors(requestBody);

      // Check if there were validation errors
      if (result.errors && result.errors.length > 0) {
        // Throw the first error as a BadRequestException
        throw new BadRequestException(result.errors[0].message);
      }

      // Add specific response data for scheduled transfers
      if (isScheduledTransfer) {
        return {
          ...result,
          isScheduled: true,
          scheduledFor: requestBody.transferAt,
          isOOO: !!requestBody.revertAt,
          revertAt: requestBody.revertAt,
          message:
            'Transfer has been scheduled and will be processed at the specified time',
        };
      }

      return result;
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  @Delete('/bulk-transfer/:id')
  @RequireCapabilities(Capability.EDIT_DOCTORS)
  async deleteBulkTransfer(
    @Param('id') bulkTransferId: string,
    @Query('doctorId') doctorId: string,
  ) {
    try {
      if (!doctorId) {
        throw new BadRequestException('Doctor ID is required');
      }
      return await this.patientTransferService.deleteBulkTransfer(
        doctorId,
        bulkTransferId,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Patch('/bulk-transfer/:id')
  @RequireCapabilities(Capability.EDIT_DOCTORS)
  async updateBulkTransfer(
    @Param('id') bulkTransferId: string,
    @Query('doctorId') doctorId: string,
    @Body() updateData: UpdateBulkTransferDto,
  ) {
    try {
      if (!doctorId) {
        throw new BadRequestException('Doctor ID is required');
      }
      return await this.patientTransferService.updateBulkTransfer(
        doctorId,
        bulkTransferId,
        updateData,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('forgot-password')
  async forgotPassword(@Body() requestBody: UserForgotPasswordDto) {
    try {
      await this.userForgotPasswordUseCaseService.execute(requestBody);
    } catch {
      // to prevent email enumeration attacks
    }

    // Always return success regardless of whether email exists
    return {
      status: 201,
      message: 'Password reset email sent successfully',
    };
  }
}
