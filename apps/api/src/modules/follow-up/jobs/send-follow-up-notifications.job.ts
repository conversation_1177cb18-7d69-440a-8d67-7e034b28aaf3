import { AuditService } from '@/modules/audit-log/audit-log.service';
import { segmentTrackEvents } from '@/modules/shared/events';
import { SegmentTrack } from '@/modules/shared/types/events';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { formatInTimeZone } from 'date-fns-tz';

@Injectable()
export class SendFollowUpNotificationsJob {
  private readonly BATCH_SIZE = 50;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly auditService: AuditService,
  ) {}

  async run(): Promise<void> {
    const NY_TIMEZONE = 'America/New_York';
    const nyDateStr = formatInTimeZone(new Date(), NY_TIMEZONE, 'yyyy-MM-dd');
    const nyDateStartOfDayUTC = new Date(`${nyDateStr}T00:00:00.000Z`);

    const scheduledToday = await this.prismaService.patientFollowUp.findMany({
      where: {
        status: 'scheduled',
        isStartNotificationSent: false,
        scheduledAt: {
          lte: nyDateStartOfDayUTC,
        },
      },
      select: {
        id: true,
        status: true,
        scheduledAt: true,
        patient: {
          select: {
            id: true,
            userId: true,
          },
        },
      },
      take: this.BATCH_SIZE,
    });

    for (const followUp of scheduledToday) {
      try {
        this.eventEmitter.emit(segmentTrackEvents.followUpSent.event, {
          event: segmentTrackEvents.followUpSent.name,
          userId: followUp.patient.userId,
          properties: {
            patientID: followUp.patient.id,
            date: followUp.scheduledAt,
          },
        } satisfies SegmentTrack);
        await this.prismaService.patientFollowUp.update({
          where: {
            id: followUp.id,
          },
          data: {
            isStartNotificationSent: true,
          },
        });
        void this.auditService.append({
          patientId: followUp.patient.id,
          action: 'FOLLOW_UP_SENT',
          actorType: 'SYSTEM',
          actorId: 'SendFollowUpNotificationsJob_Cron',
          resourceType: 'FOLLOW_UP',
          resourceId: followUp.id,
          details: {},
        });
      } catch (error) {
        console.error(
          `Error sending '${segmentTrackEvents.followUpSent.name}' event for ${followUp.id}:`,
          error,
        );
      }
    }
  }
}
