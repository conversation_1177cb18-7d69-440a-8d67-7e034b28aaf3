import { ActorRefFrom, assign, setup, SnapshotFrom } from 'xstate';

type YesNo = 'yes' | 'no';
export type FollowUpMachineContext = {
  rejected?: boolean;
  questionnaireCompleted: boolean;
  completedAt?: string;
  questionnaire: {
    startWeight?: number;
    currentWeight?: number;
    goalWeight?: number;
    areThereMedicalHistoryChanges?: YesNo;
    medicalHistoryChanges?: string;
    areThereMedicationSideEffects?: YesNo;
    medicationSideEffects?: string;
    isContinueMedication?: YesNo;
    stopMedicationReason?: string;
    isContinueCurrentDose: YesNo;
    isIncreaseSupply?: YesNo;
    changeDoseReason?: string;
    questions?: string;
    moreInfo?: string;
  };
};

type FollowUpMachine = ReturnType<typeof followUpMachine.createMachine>;
export type FollowUpActor = ActorRefFrom<FollowUpMachine>;
export type FollowUpSnapshot = SnapshotFrom<FollowUpMachine>;

export const followUpMachine = setup({
  types: {
    context: {} as FollowUpMachineContext,
  },
  guards: {
    hasChosenNotToContinueCurrentDose: function ({ context }) {
      return context.questionnaire.isContinueCurrentDose === 'no';
    },
  },
  actions: {
    complete: assign(() => {
      return {
        questionnaireCompleted: true,
        completedAt: new Date().toISOString(),
      };
    }),
    storeQuestionnaire: assign(({ context, event }, params) => {
      context.questionnaire = {
        ...context.questionnaire,
        ...event?.value,
        ...params,
      };
      if (params)
        Object.keys(params).map((key) => {
          if (params[key] === null) delete context.questionnaire[key];
        });
      return context;
    }),
  },
});

// base followUp setup
export const followUpInitialState = {
  context: {
    questionnaireCompleted: false,
    questionnaire: {},
  },
  value: 'medicalHistoryChanges',
  children: {},
  historyValue: {},
  tags: [],
};

export const followUpMachineConfig = {
  id: 'followUp',
  initial: 'medicalHistoryChanges',
  meta: { total: 10 },
  context: { questionnaireCompleted: false, questionnaire: {} },
  states: {
    medicalHistoryChanges: {
      on: {
        yes: {
          target: 'startWeight',
          actions: {
            type: 'storeQuestionnaire',
            params: { areThereMedicalHistoryChanges: 'yes' },
          },
        },
        no: {
          target: 'startWeight',
          actions: {
            type: 'storeQuestionnaire',
            params: {
              areThereMedicalHistoryChanges: 'no',
              medicalHistoryChanges: null,
            },
          },
        },
      },
      meta: { step: 1, name: 'medical-history-changes' },
    },
    startWeight: {
      on: {
        next: {
          target: 'currentWeight',
          actions: { type: 'storeQuestionnaire' },
        },
        back: { target: 'medicalHistoryChanges' },
      },
      meta: { step: 2, name: 'start-weight' },
    },
    currentWeight: {
      on: {
        next: { target: 'goalWeight', actions: 'storeQuestionnaire' },
        back: { target: 'startWeight' },
      },
      meta: { step: 3, name: 'current-weight' },
    },
    goalWeight: {
      on: {
        next: {
          target: 'medicationSideEffect',
          actions: 'storeQuestionnaire',
        },
        back: { target: 'currentWeight' },
      },
      meta: { step: 4, name: 'goal-weight' },
    },
    medicationSideEffect: {
      on: {
        yes: {
          target: 'continueMedication',
          actions: {
            type: 'storeQuestionnaire',
            params: { areThereMedicationSideEffects: 'yes' },
          },
        },
        no: {
          target: 'continueMedication',
          actions: {
            type: 'storeQuestionnaire',
            params: {
              areThereMedicationSideEffects: 'no',
              medicationSideEffects: null,
            },
          },
        },
        back: { target: 'goalWeight' },
      },
      meta: { step: 5, name: 'medication-side-effect' },
    },
    continueMedication: {
      on: {
        yes: {
          target: 'continueCurrentDose',
          actions: {
            type: 'storeQuestionnaire',
            params: {
              isContinueMedication: 'yes',
              stopMedicationReason: null,
            },
          },
        },
        no: {
          target: 'continueCurrentDose',
          actions: {
            type: 'storeQuestionnaire',
            params: { isContinueMedication: 'no' },
          },
        },
        back: { target: 'medicationSideEffect' },
      },
      meta: { step: 6, name: 'continue-medication' },
    },
    continueCurrentDose: {
      on: {
        yes: {
          target: 'questions',
          actions: {
            type: 'storeQuestionnaire',
            params: {
              isContinueCurrentDose: 'yes',
              changeDoseReason: null,
              isIncreaseSupply: null,
            },
          },
        },
        no: {
          target: 'increaseSupply',
          actions: {
            type: 'storeQuestionnaire',
            params: { isContinueCurrentDose: 'no' },
          },
        },
        back: { target: 'continueMedication' },
      },
      meta: { step: 7, name: 'continue-current-dose' },
    },
    increaseSupply: {
      on: {
        yes: {
          target: 'questions',
          actions: {
            type: 'storeQuestionnaire',
            params: { isIncreaseSupply: 'yes' },
          },
        },
        no: {
          target: 'questions',
          actions: {
            type: 'storeQuestionnaire',
            params: { isIncreaseSupply: 'no' },
          },
        },
        back: { target: 'continueCurrentDose' },
      },
      meta: { step: 8, name: 'increase-supply' },
    },
    questions: {
      on: {
        next: { target: 'moreInfo', actions: 'storeQuestionnaire' },
        back: [
          {
            guard: 'hasChosenNotToContinueCurrentDose',
            target: 'increaseSupply',
          },
          {
            target: 'continueCurrentDose',
          },
        ],
      },
      meta: { step: 9, name: 'questions' },
    },
    moreInfo: {
      on: {
        next: {
          target: 'success',
          actions: ['storeQuestionnaire', 'complete'],
        },
        back: { target: 'questions' },
      },
      meta: { step: 10, name: 'more-info' },
    },
    success: {
      on: {
        back: { target: 'moreInfo' },
      },
      meta: { name: 'success' },
    },
  },
};
