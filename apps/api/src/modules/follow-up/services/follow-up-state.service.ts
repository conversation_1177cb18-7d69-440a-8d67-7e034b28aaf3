import { QuestionnairePersistence } from '@adapters/persistence/database/questionnaire.persistence';
import { Injectable } from '@nestjs/common';
import { createActor } from 'xstate';

import {
  FollowUpActor,
  followUpMachine,
  followUpMachineConfig,
  FollowUpSnapshot,
} from '../states/follow-up.state';

@Injectable()
export class FollowUpStateService {
  constructor(
    private readonly questionnairePersistence: QuestionnairePersistence,
  ) {}

  async getCurrentFollowUpActor(version: number, snapshot: FollowUpSnapshot) {
    return await this.buildFollowUpMachine(version, snapshot);
  }

  performTransition(actor: FollowUpActor, eventName: string, data?: any) {
    const event = data ? { type: eventName, value: data } : { type: eventName };

    actor.send(event);

    const snapshot = actor.getSnapshot();

    if (snapshot.status === 'error') {
      throw new Error(
        `Error after transition: ${JSON.stringify(snapshot.value)}`,
      );
    }
    return actor;
  }

  async getCurrentFollowUpState(version: number, snapshot: FollowUpSnapshot) {
    const actor = await this.getCurrentFollowUpActor(version, snapshot);
    const followUpSnapshot = actor.getSnapshot();

    let totalSteps = 0;
    try {
      totalSteps =
        followUpSnapshot.machine.states.questionnaire.config.meta.total;
    } catch (e) {
      console.error('Error parsing Follow Up', e);
      console.error(`Version: ${version}`);
      console.error(`Snapshot: ${JSON.stringify(snapshot)}`);
      console.error(`Parsed Snapshot: ${JSON.stringify(followUpSnapshot)}`);
      return;
    }

    const step = followUpSnapshot.machine['idMap'].get(
      `followUp.${followUpSnapshot.value}`,
    );

    if (!step) {
      throw new Error('step not found');
    }

    const currentStep = step.meta?.step ?? 1;
    const currentStepName = step.meta?.name;

    return {
      state: followUpSnapshot.value,
      context: followUpSnapshot.context,
      events: followUpSnapshot._nodes.flatMap((sn) => sn.ownEvents),
      totalSteps,
      currentStep: {
        name: currentStepName,
        number: currentStep,
      },
    };
  }

  private async buildFollowUpMachine(
    version?: number,
    snapshot?: FollowUpSnapshot,
  ) {
    const questionnaire = version
      ? await this.questionnairePersistence.getLastQuestionnaire('followUp')
      : await this.questionnairePersistence.getQuestionnaireByVersion(
          'followUp',
          version,
        );

    const config = JSON.parse(JSON.stringify(followUpMachineConfig));
    config.states.questionnaire = questionnaire.config;

    const machine = followUpMachine.createMachine(config);

    const actor = snapshot
      ? createActor(machine, { snapshot: this.recoverSnapshot(snapshot) })
      : createActor(machine);

    return actor.start();
  }

  private recoverSnapshot(snapshot: FollowUpSnapshot) {
    if (snapshot.status === 'error') {
      console.warn(`Recovering snapshot for treatment`);
      return {
        ...snapshot,
        status: 'active',
        error: undefined,
      } as FollowUpSnapshot;
    }
    return snapshot;
  }
}
