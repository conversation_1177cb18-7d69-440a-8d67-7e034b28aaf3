import { ExecutionContext, runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { segmentTrackEvents } from '@/modules/shared/events';
import { OutboxerService } from '@/modules/shared/outboxer/outboxer.service';
import {
  Doctor<PERSON><PERSON><PERSON><PERSON>,
  DoctorProfile,
} from '@adapters/persistence/database/doctor.persistence';
import {
  PatientFollowUpProfile,
  PatientPersistence,
  sanitizeMachineStateForDB,
} from '@adapters/persistence/database/patient.persistence';
import { QuestionnairePersistence } from '@adapters/persistence/database/questionnaire.persistence';
import { AnalyzeFollowUpUseCase } from '@modules/ai/use-cases/analyze-follow-up.use-case';
import {
  followUpInitialState,
  FollowUpSnapshot,
} from '@modules/follow-up/states/follow-up.state';
import { HttpException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PatientFollowUpAutoPrescribe, Prisma } from '@prisma/client';
import { isPast } from 'date-fns';
import { Request } from 'express';

import {
  PrismaService,
  PrismaTransactionalClient,
} from '../../prisma/prisma.service';

@Injectable()
export class FollowUpService {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly doctorPersistence: DoctorPersistence,
    private readonly prisma: PrismaService,
    private readonly questionnairePersistence: QuestionnairePersistence,
    private readonly analyzeFollowUp: AnalyzeFollowUpUseCase,
    private readonly eventEmitter: EventEmitter2,
    private readonly auditService: AuditService,
    private readonly outboxer: OutboxerService,
  ) {}

  async getActive(
    patientId: string,
    { prisma }: { prisma?: PrismaTransactionalClient } = {
      prisma: this.prisma,
    },
  ) {
    return prisma.patientFollowUp.findFirst({
      where: { patientId, status: 'scheduled' },
    });
  }

  async create(
    patientId: string,
    treatmentId: string,
    scheduledAt: Date,
    ctx: ExecutionContext = { prisma: this.prisma },
  ) {
    if (isPast(scheduledAt)) {
      throw new Error('The scheduled date cannot be in the past.');
    }

    return runInDbTransaction(
      ctx.prisma,
      async (prisma: PrismaTransactionalClient) => {
        const followUpQuestionnaire =
          await this.questionnairePersistence.getLastQuestionnaire('followUp');

        const followUp = await this.prisma.patientFollowUp.create({
          data: {
            patientId,
            treatmentId,
            status: 'scheduled',
            questionnaireId: followUpQuestionnaire.id,
            questionnaireState: followUpInitialState,
            scheduledAt: scheduledAt,
          },
        });

        await this.outboxer.enqueue(
          followUp.patientId,
          'follow-up-updated',
          {
            event: 'created',
            followUp: followUp,
          },
          { prisma },
        );

        await this.auditService.append(
          {
            patientId: followUp.patientId,
            action: 'FOLLOW_UP_CREATED',
            actorType: 'SYSTEM',
            actorId: 'SYSTEM',
            resourceType: 'FOLLOW_UP',
            resourceId: followUp.id,
            details: {
              questionnaireId: followUpQuestionnaire.id,
              questionnaireVersion: followUpQuestionnaire.version,
              scheduledAt: scheduledAt.toISOString(),
              treatmentId: treatmentId,
            },
          },
          { prisma: prisma },
        );

        return followUp;
      },
    );
  }

  async moveScheduledAt(
    followUpId: string,
    scheduledAt: Date,
    ctx: ExecutionContext = {
      prisma: this.prisma,
    },
  ) {
    return runInDbTransaction(
      ctx.prisma,
      async (prisma: PrismaTransactionalClient) => {
        const followUp = await prisma.patientFollowUp.update({
          where: { id: followUpId },
          data: { scheduledAt },
        });

        this.eventEmitter.emit(segmentTrackEvents.followUpCancelled.event, {
          event: segmentTrackEvents.followUpCancelled.name,
          userId: followUp.patientId,
          properties: {
            patientID: followUp.patientId,
          },
        });

        await this.outboxer.enqueue(
          followUp.patientId,
          'follow-up-updated',
          {
            event: 'rescheduled',
            followUp: followUp,
          },
          { prisma },
        );

        await this.auditService.append(
          {
            patientId: followUp.patientId,
            action: 'FOLLOW_UP_RESCHEDULED',
            actorType: 'SYSTEM',
            actorId: 'SYSTEM',
            resourceType: 'FOLLOW_UP',
            resourceId: followUp.id,
            details: {
              scheduledAt: scheduledAt.toISOString(),
            },
          },
          { prisma },
        );

        return followUp;
      },
    );
  }

  async getPatientData(req: Request): Promise<PatientFollowUpProfile> {
    const cognitoId = req.user['userId'];
    if (!cognitoId) throw new Error('Missing cognitoId in request');

    // if there is no patient data in the cache, get it from the database
    const patient = await this.patientPersistence.getFollowUpProfile(cognitoId);
    if (!patient)
      throw new Error(`Patient not found for username: ${cognitoId}`);

    return patient;
  }

  async getDoctorData(req: Request): Promise<DoctorProfile> {
    const cognitoId = req.user['userId'];
    if (!cognitoId) throw new Error('Missing cognitoId in request');

    // if there is no patient data in the cache, get it from the database
    const doctor = await this.doctorPersistence.getProfile(cognitoId);
    if (!doctor) throw new Error(`Doctor not found for username: ${cognitoId}`);

    return doctor;
  }

  async getPatientInProgressFollowUp(patientId: string) {
    return await this.prisma.patientFollowUp.findFirst({
      where: {
        patientId: patientId,
        status: { in: ['scheduled', 'completedByPatient'] },
        scheduledAt: { lte: new Date() },
      },
      include: {
        patient: { select: { id: true, userId: true } },
        questionnaire: { select: { id: true, version: true } },
        treatment: { select: { id: true, state: true } },
      },
      orderBy: { scheduledAt: 'desc' },
    });
  }

  async updateFollowUpState(
    id: string,
    state: FollowUpSnapshot,
    data?: Omit<Prisma.PatientFollowUpUpdateInput, 'questionnaireState'>,
    ctx: ExecutionContext = { prisma: this.prisma },
  ) {
    const update = { questionnaireState: state, ...data };
    const completed = state.context.questionnaireCompleted;
    if (completed) {
      update.completedAt = new Date();
      update.status = 'completedByPatient';
    }

    return runInDbTransaction(
      ctx.prisma,
      async (prisma: PrismaTransactionalClient) => {
        const result = await prisma.patientFollowUp.update({
          where: { id },
          data: sanitizeMachineStateForDB(update),
        });

        if (completed) {
          await this.outboxer.enqueue(
            result.patientId,
            'follow-up-updated',
            {
              event: 'completed-by-patient',
              followUp: result,
            },
            { prisma },
          );
          await this.auditService.append(
            {
              patientId: result.patientId,
              action: 'FOLLOW_UP_COMPLETED',
              actorType: 'PATIENT',
              actorId: result.patientId,
              resourceType: 'FOLLOW_UP',
              resourceId: result.id,
              details: {
                answers: state.context.questionnaire,
              },
            },
            {
              prisma: prisma,
            },
          );
        }

        return result;
      },
    );
  }

  async cancel(id: string, ctx: ExecutionContext = { prisma: this.prisma }) {
    return runInDbTransaction(
      ctx.prisma,
      async (prisma: PrismaTransactionalClient) => {
        const followUp = await prisma.patientFollowUp.findUniqueOrThrow({
          where: { id },
        });

        if (followUp.status !== 'scheduled') {
          throw new HttpException(
            `You can't cancel ${followUp.status}ed follow up`,
            400,
          );
        }

        const update = await prisma.patientFollowUp.update({
          where: { id },
          data: { status: 'cancelled' },
          include: { patient: { select: { id: true, userId: true } } },
        });

        await this.outboxer.enqueue(
          update.patientId,
          'follow-up-updated',
          {
            event: 'canceled',
            followUp: update,
          },
          { prisma },
        );

        await this.auditService.append(
          {
            patientId: followUp.patientId,
            action: 'FOLLOW_UP_CANCELLED',
            actorType: 'SYSTEM',
            actorId: 'SYSTEM',
            resourceType: 'FOLLOW_UP',
            resourceId: followUp.id,
            details: {
              treatmentId: update.treatmentId,
              cancelledAt: new Date().toISOString(),
            },
          },
          { prisma: prisma },
        );

        return update;
      },
    );
  }

  async calculateAutoPrescribe(
    state: FollowUpSnapshot,
  ): Promise<PatientFollowUpAutoPrescribe> {
    const questionnaire = state.context.questionnaire;

    let questionsAnalysis: { isEmpty: boolean };
    let moreInfoAnalysis: { isEmpty: boolean };

    // Previous implementation without AI for the moment
    if (process.env.ENVIRONMENT === 'production') {
      questionsAnalysis = {
        isEmpty: !(questionnaire.questions?.trim() ?? ''),
      };
      moreInfoAnalysis = {
        isEmpty: !(questionnaire.moreInfo?.trim() ?? ''),
      };
    } else {
      [questionsAnalysis, moreInfoAnalysis] = await Promise.allSettled([
        !questionnaire.questions.trim()
          ? Promise.resolve({ isEmpty: true })
          : this.analyzeFollowUp.execute('questions', questionnaire.questions),
        !questionnaire.moreInfo.trim()
          ? Promise.resolve({ isEmpty: true })
          : this.analyzeFollowUp.execute('moreInfo', questionnaire.moreInfo),
      ]).then((results) =>
        results.map((result) =>
          result.status === 'fulfilled' ? result.value : { isEmpty: false },
        ),
      );
    }

    const meetsBaselineCriteria =
      questionnaire.areThereMedicalHistoryChanges === 'no' &&
      questionnaire.areThereMedicationSideEffects === 'no' &&
      questionnaire.isContinueMedication === 'yes' &&
      questionsAnalysis.isEmpty &&
      moreInfoAnalysis.isEmpty;

    // If baseline criteria are not met, do not auto-prescribe
    if (!meetsBaselineCriteria) {
      return PatientFollowUpAutoPrescribe.no;
    }

    // Check for Automation #1 - Static Refill/Continue Dosage
    if (questionnaire.isContinueCurrentDose === 'yes') {
      return PatientFollowUpAutoPrescribe.currentDose;
    }

    // Check for Automation #2 - Ascending Ladder Refill/Increase Dosage
    if (questionnaire.isIncreaseSupply === 'yes') {
      return PatientFollowUpAutoPrescribe.increasedDose;
    }

    // If none of the automation criteria are met
    return PatientFollowUpAutoPrescribe.no;
  }
}
