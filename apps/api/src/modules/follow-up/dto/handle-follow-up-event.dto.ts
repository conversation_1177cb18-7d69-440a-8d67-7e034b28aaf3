import { IsEnum, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class HandleFollowUpEventDto {
  @IsNumber()
  @IsOptional()
  startWeight?: number;

  @IsNumber()
  @IsOptional()
  currentWeight?: number;

  @IsNumber()
  @IsOptional()
  goalWeight?: number;

  @IsEnum(['yes', 'no'])
  @IsOptional()
  areThereMedicalHistoryChanges?: 'yes' | 'no';

  @IsString()
  @IsOptional()
  medicalHistoryChanges?: string;

  @IsEnum(['yes', 'no'])
  @IsOptional()
  areThereMedicationSideEffects?: 'yes' | 'no';

  @IsString()
  @IsOptional()
  medicationSideEffects?: string;

  @IsEnum(['yes', 'no'])
  @IsOptional()
  isContinueMedication?: 'yes' | 'no';

  @IsString()
  @IsOptional()
  stopMedicationReason?: string;

  @IsEnum(['yes', 'no'])
  @IsOptional()
  isContinueCurrentDose?: 'yes' | 'no';

  @IsString()
  @IsOptional()
  changeDoseReason?: string;

  @IsString()
  @IsOptional()
  questions?: string;

  @IsString()
  @IsOptional()
  moreInfo?: string;
}
