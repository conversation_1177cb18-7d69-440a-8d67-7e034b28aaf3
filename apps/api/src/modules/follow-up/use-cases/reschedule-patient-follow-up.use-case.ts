import { PrismaService } from '@/modules/prisma/prisma.service';
import { FollowUpService } from '@modules/follow-up/services/follow-up.service';
import { HttpException, Injectable } from '@nestjs/common';

import { RescheduleFollowUpDto } from '../dto/reschedule-follow-up.dto';

@Injectable()
export class ReschedulePatientFollowUpUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly followUpService: FollowUpService,
  ) {}

  async execute(followUpId: string, data: RescheduleFollowUpDto) {
    const followUp = await this.prismaService.patientFollowUp.findUnique({
      where: { id: followUpId },
    });

    if (!followUp) {
      throw new HttpException('Follow up not found', 404);
    }

    if (followUp.status !== 'scheduled') {
      throw new HttpException(
        `Cannot reschedule a follow up that is ${followUp.status}`,
        400,
      );
    }

    if (followUp.scheduledAt <= new Date()) {
      throw new HttpException(
        `Cannot reschedule an in progress follow-up ${followUp.status}`,
        400,
      );
    }

    return await this.followUpService.moveScheduledAt(
      followUpId,
      data.scheduleAt,
    );
  }
}
