import { AllQueuesInsightsUseCase } from '@modules/insights/use-cases/all-queues-insights.use-case';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class InsightsWorker implements OnModuleInit {
  private readonly disabled: boolean;

  constructor(
    private readonly allQueuesInsightsUseCase: AllQueuesInsightsUseCase,
  ) {
    this.disabled =
      process.env.IS_CLI === 'true' || process.env.ENVIRONMENT === 'local';
  }

  async onModuleInit() {
    await this.warmupQueuesInsightsCache();
  }

  // every 2 minutes
  @Cron('*/2 * * * *')
  async warmupQueuesInsightsCache() {
    if (this.disabled) return;
    await this.allQueuesInsightsUseCase.execute({ forceRefresh: false });
  }
}
