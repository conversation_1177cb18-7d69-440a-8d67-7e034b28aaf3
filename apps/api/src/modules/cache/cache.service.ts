import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { Cache } from 'cache-manager';

// Simple implementation of runInNextTick for backend use
const runInNextTick = (cb: () => void) => {
  setTimeout(() => {
    try {
      cb();
    } catch (error) {
      console.error('runInNextTick failed', error);
    }
  });
};

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private keyPrefix: string = '';

  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    @Optional() prefix?: string,
  ) {
    if (prefix) {
      this.keyPrefix = prefix;
    }
  }

  /**
   * Set a prefix for all cache keys
   * @param prefix The prefix to use for all cache keys
   */
  setPrefix(prefix: string): void {
    this.keyPrefix = prefix;
  }

  /**
   * Clear the current prefix
   */
  clearPrefix(): void {
    this.keyPrefix = '';
  }

  /**
   * Get the current prefix
   */
  getPrefix(): string {
    return this.keyPrefix;
  }

  /**
   * Apply the prefix to a key if a prefix is set
   * @param key The original key
   * @returns The key with prefix applied if a prefix is set
   */
  private applyPrefix(key: string): string {
    return this.keyPrefix ? `${this.keyPrefix}:${key}` : key;
  }

  /**
   * Get a value from cache
   * @param key The cache key (will be prefixed if a prefix is set)
   */
  async get<T>(key: string): Promise<T> {
    const prefixedKey = this.applyPrefix(key);
    try {
      return this.cacheManager.get<T>(prefixedKey);
    } catch (error) {
      this.logger.warn(`Error getting value for key ${prefixedKey}:`, error);
      return null as any;
    }
  }

  /**
   * Set a value in cache with expiration time in seconds
   * @param key The cache key (will be prefixed if a prefix is set)
   * @param value The value to store
   * @param seconds TTL in seconds
   */
  async set<T>(key: string, value: T, seconds: number): Promise<void> {
    const prefixedKey = this.applyPrefix(key);
    await this.cacheManager.set(prefixedKey, value, { ttl: seconds } as any);
  }

  /**
   * Delete a key from cache
   * @param key The cache key (will be prefixed if a prefix is set)
   */
  async del(key: string): Promise<void> {
    const prefixedKey = this.applyPrefix(key);
    await this.cacheManager.del(prefixedKey);
  }

  /**
   * Get from cache or execute callback to get fresh data
   * @param key The cache key (will be prefixed if a prefix is set)
   * @param seconds TTL in seconds
   * @param callback Function to generate a fresh value if needed
   */
  async remember<T>(
    key: string,
    seconds: number,
    callback: () => Promise<T>,
  ): Promise<T> {
    const prefixedKey = this.applyPrefix(key);
    const cached = await this.cacheManager.get<T>(prefixedKey);
    if (cached !== undefined && cached !== null) {
      return cached;
    }
    const fresh = await callback();
    await this.cacheManager.set(prefixedKey, fresh, { ttl: seconds } as any);
    return fresh;
  }

  /**
   * List all cache keys with the current prefix
   * @returns Array of cache keys (with prefix applied if set)
   */
  async list(): Promise<string[]> {
    try {
      // Get the underlying Redis store
      const store = this.cacheManager.store as any;
      const redisClient = store.getClient();

      if (!store || !redisClient) {
        this.logger.warn('Redis client not available for listing keys');
        return [];
      }

      // const redisClient = store.client;

      if (this.keyPrefix) {
        const pattern = `${this.keyPrefix}:*`;
        return await redisClient.keys(pattern);
      } else {
        // No prefix set, list all keys
        return await redisClient.keys('*');
      }
    } catch (error) {
      this.logger.error(`Error listing cache keys: ${error.message}`);
      return [];
    }
  }

  /**
   * Implements the stale-while-revalidate caching pattern.
   *
   * @param key The cache key
   * @param ttl An array with two values: [freshSeconds, staleSeconds]
   *            - freshSeconds: How long the cache is considered fresh
   *            - staleSeconds: How long the cache can be served while stale
   * @param callback Function to generate a fresh value if needed
   * @returns The cached value (fresh or stale) or a newly generated value
   *
   * If a request is made within the fresh period, the cache is returned immediately.
   * If a request is made during the stale period, the stale value is served to the user,
   * and a background process is triggered to refresh the cached value.
   * If a request is made after the stale period, the cache is recalculated immediately.
   */
  async flexible<T>(
    key: string,
    ttl: [number, number],
    callback: () => Promise<T>,
  ): Promise<T> {
    const prefixedKey = this.applyPrefix(key);
    const [freshSeconds, staleSeconds] = ttl;

    try {
      // Try to get the cached value and its metadata
      const cachedData = await this.cacheManager.get<{
        value: T;
        timestamp: number;
      }>(prefixedKey);

      if (cachedData) {
        const { value, timestamp } = cachedData;
        const now = Date.now();
        const ageInSeconds = (now - timestamp) / 1000;

        // If the cache is fresh, return it immediately
        if (ageInSeconds < freshSeconds) {
          return value;
        }

        // If the cache is stale but still within the stale window
        if (ageInSeconds < staleSeconds) {
          // Schedule a background refresh
          runInNextTick(async () => {
            try {
              const fresh = await callback();
              await this.cacheManager.set(
                prefixedKey,
                { value: fresh, timestamp: Date.now() },
                { ttl: staleSeconds } as any,
              );
            } catch (error) {
              this.logger.error(
                `Background refresh failed for key ${prefixedKey}:`,
                error,
              );
            }
          });

          // Return the stale value immediately
          return value;
        }
      }

      // Cache is either expired or doesn't exist, generate a fresh value
      const fresh = await callback();
      await this.cacheManager.set(
        prefixedKey,
        { value: fresh, timestamp: Date.now() },
        { ttl: staleSeconds } as any,
      );
      return fresh;
    } catch (error) {
      this.logger.warn(
        `Error in flexible cache for key ${prefixedKey}:`,
        error,
      );
      // If there's an error with the cache, fall back to generating a fresh value
      return callback();
    }
  }
}
