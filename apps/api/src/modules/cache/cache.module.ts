import { RedisOptions } from '@/config';
import { CACHE_MANAGER, CacheModule } from '@nestjs/cache-manager';
import { DynamicModule, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';

import { CacheService } from './cache.service';
import { RedlockService } from './redlock.service';

// Define the interface for the module options
export interface AppCacheModuleOptions {
  prefix?: string;
}

@Module({
  imports: [CacheModule.registerAsync(RedisOptions)],
  providers: [
    {
      provide: CacheService,
      useFactory: (cacheManager: Cache, configService: ConfigService) => {
        const service = new CacheService(cacheManager);

        // Get worktree name from environment variables
        const worktreeName = configService.get<string>('WORKTREE_NAME');
        const environment =
          configService.get<string>('ENVIRONMENT') || 'development';

        // Set prefix if worktree name exists
        if (worktreeName) {
          service.setPrefix(`${worktreeName}${environment}`);
        }

        return service;
      },
      inject: [CACHE_MANAGER, ConfigService],
    },
    RedlockService,
  ],
  exports: [CacheService, RedlockService],
})
export class AppCacheModule {
  /**
   * Register the cache module with a global prefix
   * @param options Configuration options
   * @returns A dynamic module with the configured prefix
   */
  static register(options?: AppCacheModuleOptions): DynamicModule {
    return {
      module: AppCacheModule,
      imports: [CacheModule.registerAsync(RedisOptions)],
      providers: [
        {
          provide: CacheService,
          useFactory: (cacheManager: Cache, configService: ConfigService) => {
            const service = new CacheService(cacheManager);

            // Get worktree name from environment variables
            const worktreeName = configService.get<string>('WORKTREE_NAME');
            const environment =
              configService.get<string>('ENVIRONMENT') || 'development';

            // Apply prefix from options or worktree name
            if (options?.prefix) {
              service.setPrefix(options.prefix);
            } else if (worktreeName) {
              service.setPrefix(`${worktreeName}:${environment}`);
            }

            return service;
          },
          inject: [CACHE_MANAGER, ConfigService],
        },
        RedlockService,
      ],
      exports: [CacheService, RedlockService],
    };
  }
}
