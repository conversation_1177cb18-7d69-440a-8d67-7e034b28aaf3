import type {
  IPharmacyService,
  MultiplePrescriptionRequest,
  MultiplePrescriptionResponse,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

interface EpiqRxItem {
  rx_id: string;
  medication_name: string;
  quantity_dispensed: number;
  days_supply?: number;
  medication_sig: string;
  is_refill: boolean;
}

interface EpiqPayload {
  order_id: string;
  patient_first_name: string;
  patient_last_name: string;
  patient_dob: string;
  patient_gender: string;
  patient_address1: string;
  patient_city: string;
  patient_state: string;
  patient_zip: string;
  patient_country: string;
  patient_cell_phone: string;
  patient_known_allergies?: string;
  patient_other_medications?: string;
  is_payment_success?: boolean;
  prescriber_npi: string;
  prescriber_first_name: string;
  prescriber_last_name: string;
  rx_items: EpiqRxItem[];
  notes?: string;
}

@Injectable()
export class EpiqPharmacyService implements IPharmacyService {
  private readonly logger = new Logger(EpiqPharmacyService.name);
  private readonly httpClient: AxiosInstance;
  private readonly baseUrl: string;
  private readonly username: string;
  private readonly password: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.baseUrl = this.configService.getOrThrow<string>('EPIQ_API_URL');
    this.username = this.configService.getOrThrow<string>('EPIQ_API_USERNAME');
    this.password = this.configService.getOrThrow<string>('EPIQ_API_PASSWORD');

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      auth: { username: this.username, password: this.password },
    });
  }

  async submitPrescriptions(
    request: MultiplePrescriptionRequest,
  ): Promise<MultiplePrescriptionResponse> {
    if (request.prescriptions.length === 0) {
      return {
        success: true,
        message: 'No prescriptions to submit',
        results: [],
      };
    }

    // For Epiq, we need to bundle all prescriptions into a single request
    // We'll use the first prescription for patient/prescriber info (they should all be the same)
    const firstPrescription = request.prescriptions[0];

    // Combine all rx_items from all prescriptions
    const allRxItems: EpiqRxItem[] = [];
    for (const prescription of request.prescriptions) {
      const rxItems = prescription.products.map((product) => ({
        rx_id: product.externalId,
        medication_name: product.drugDescription,
        quantity_dispensed: product.quantity,
        days_supply: product.daysSupply,
        medication_sig: product.sig,
        is_refill: false,
      }));
      allRxItems.push(...rxItems);
    }

    // Build the combined payload
    const payload: EpiqPayload = {
      order_id: `${request.prescriptions.map((p) => p.prescriptionId).join('-')}`,
      patient_first_name: firstPrescription.patient.firstName,
      patient_last_name: firstPrescription.patient.lastName,
      patient_dob: firstPrescription.patient.dateOfBirth,
      patient_gender:
        firstPrescription.patient.gender === 'male' ? 'Male' : 'Female',
      patient_address1: firstPrescription.patient.address.street1,
      patient_city: firstPrescription.patient.address.city,
      patient_state: firstPrescription.patient.address.state,
      patient_zip: firstPrescription.patient.address.zipCode,
      patient_country: firstPrescription.patient.address.country || 'US',
      patient_cell_phone: firstPrescription.patient.phoneNumber,
      patient_known_allergies:
        firstPrescription.patient.knownAllergies?.join(', ') || '',
      patient_other_medications:
        firstPrescription.originalTreatmentDetails?.medications || '',
      is_payment_success:
        firstPrescription.originalTreatmentDetails?.paymentSuccess || true,
      prescriber_npi: firstPrescription.prescriber.npi,
      prescriber_first_name: firstPrescription.prescriber.firstName,
      prescriber_last_name: firstPrescription.prescriber.lastName,
      rx_items: allRxItems,
      notes: '', // TBD?
    };

    let responseData: any = null;
    let responseStatus = 500;

    try {
      const response = await this.httpClient.post(
        '/api/receivedPrescription',
        payload,
        { headers: { 'Content-Type': 'application/json' } },
      );

      responseData = response.data;
      responseStatus = response.status;

      // Log successful integration (one record per API request)
      await this.logPharmacyIntegration(
        firstPrescription,
        payload,
        responseData,
        responseStatus,
        payload.order_id,
      );

      if (response.status === 200 || response.status === 201) {
        // All prescriptions in the bundle succeeded
        const results = request.prescriptions.map((prescription) => ({
          treatmentId: prescription.treatmentId,
          prescriptionId: prescription.prescriptionId,
          success: true,
          orderId: prescription.treatmentId,
          pharmacyOrderId: response.data?.order_id || payload.order_id,
          message: 'Prescription submitted successfully to Epiq',
        }));

        return {
          success: true,
          message: `All ${request.prescriptions.length} prescriptions submitted successfully to Epiq`,
          results,
          rawResponse: response.data,
        };
      } else {
        // All prescriptions in the bundle failed
        const results = request.prescriptions.map((prescription) => ({
          treatmentId: prescription.treatmentId,
          prescriptionId: prescription.prescriptionId,
          success: false,
          message: `Unexpected response status: ${response.status}`,
          errors: [
            { message: `Unexpected response status: ${response.status}` },
          ],
        }));

        return {
          success: false,
          message: `Unexpected response status: ${response.status}`,
          results,
          rawResponse: response.data,
        };
      }
    } catch (error) {
      this.logger.error('Failed to submit prescriptions to Epiq', error);

      let errorMessage = 'Unknown error';
      if (error.isAxiosError && error.response) {
        responseStatus = error.response.status;
        responseData = error.response.data;
        errorMessage =
          responseData?.message ||
          responseData?.error ||
          responseData?.detail ||
          error.message;

        // Log failed integration (one record per API request)
        await this.logPharmacyIntegration(
          firstPrescription,
          payload,
          responseData,
          responseStatus,
          payload.order_id,
        );
      }

      // All prescriptions in the bundle failed
      const results = request.prescriptions.map((prescription) => ({
        treatmentId: prescription.treatmentId,
        prescriptionId: prescription.prescriptionId,
        success: false,
        message: errorMessage,
        errors: [{ message: errorMessage }],
      }));

      return {
        success: false,
        message: errorMessage,
        results,
        rawResponse: responseData,
      };
    }
  }

  private async logPharmacyIntegration(
    request: PrescriptionRequest,
    apiPayload: any,
    responseData: any,
    responseStatus: number,
    orderId: string,
  ): Promise<void> {
    try {
      // Get pharmacy ID from slug
      const pharmacy = await this.prisma.pharmacy.findFirst({
        where: { slug: 'epiqScripts' },
      });

      if (!pharmacy) {
        this.logger.warn('Could not find Epiq pharmacy record for logging');
        return;
      }

      await this.prisma.pharmacyIntegration.create({
        data: {
          pharmacyId: pharmacy.id,
          prescriptionId: request.prescriptionId,
          orderId: orderId, // For Epiq, this is the order_id from the request
          request: apiPayload,
          response: responseData || {},
          responseStatus,
        },
      });
    } catch (error) {
      this.logger.error('Failed to log pharmacy integration', error);
      // Don't throw - logging failure shouldn't break the main flow
    }
  }
}
