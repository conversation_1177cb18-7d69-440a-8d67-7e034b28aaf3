import type {
  IPharmacyService,
  MultiplePrescriptionRequest,
  MultiplePrescriptionResponse,
  PharmacyPatient,
  PharmacyPrescriber,
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { format } from 'date-fns';

import { PrescriptionImageGeneratorService } from './prescription-image-generator.service';

interface RedRockConfig {
  baseUrl: string;
  storeId: string;
  facilityId: string;
  chargeAccountId: string;
  username: string;
  password: string;
}

interface RedRockEntityPayload {
  StoreID: string;
  Entity: string;
  Field: string[];
}

interface RedRockEntityResponse {
  [key: string]: string;
}

@Injectable()
export class RedRockPharmacyService implements IPharmacyService {
  private readonly logger = new Logger(RedRockPharmacyService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prescriptionImageGenerator: PrescriptionImageGeneratorService,
    private readonly prisma: PrismaService,
  ) {}

  private urlEncodeSearchValue(value: string): string {
    // URL encode special characters that could appear in search values
    return encodeURIComponent(value);
  }

  async submitPrescriptions(
    request: MultiplePrescriptionRequest,
  ): Promise<MultiplePrescriptionResponse> {
    const results: MultiplePrescriptionResponse['results'] = [];

    // RedRock requires individual requests for each prescription
    for (const prescription of request.prescriptions) {
      try {
        const response = await this.processPrescription(prescription);

        results.push({
          treatmentId: prescription.treatmentId,
          prescriptionId: prescription.prescriptionId,
          success: response.success,
          orderId: response.orderId,
          pharmacyOrderId: response.pharmacyOrderId,
          message: response.message,
          errors: response.errors,
        });
      } catch (error) {
        this.logger.error(
          `Failed to submit prescription for treatment ${prescription.treatmentId}:`,
          error,
        );

        results.push({
          treatmentId: prescription.treatmentId,
          prescriptionId: prescription.prescriptionId,
          success: false,
          message: error.message || 'Unknown error',
          errors: [{ message: error.message || 'Unknown error' }],
        });
      }
    }

    const allSuccess = results.every((r) => r.success);
    const successCount = results.filter((r) => r.success).length;

    return {
      success: allSuccess,
      message: allSuccess
        ? `All ${results.length} prescriptions submitted successfully to RedRock`
        : `${successCount} of ${results.length} prescriptions submitted successfully to RedRock`,
      results,
      rawResponse: results.map((r) => r.pharmacyOrderId).filter(Boolean),
    };
  }

  private async processPrescription(request: PrescriptionRequest): Promise<{
    success: boolean;
    message?: string;
    orderId?: string;
    pharmacyOrderId?: string;
    errors?: Array<{ field?: string; message: string }>;
    rawResponse?: any;
  }> {
    const prescriptionResponses: any[] = [];
    const prescriptionOrderIds: string[] = [];

    try {
      const config = this.getRedRockConfig(request);
      const httpClient = axios.create({ baseURL: config.baseUrl });

      const token = await this.getAuthToken(
        httpClient,
        config.username,
        config.password,
      );
      const prescriberId = await this.getOrCreatePrescriber(
        httpClient,
        token,
        config.storeId,
        request.prescriber,
      );
      const patientId = await this.getOrCreatePatient(
        httpClient,
        token,
        config.storeId,
        request.patient,
        config.facilityId,
        config.chargeAccountId,
      );

      for (const product of request.products) {
        let prescriptionPayload: any = null;
        let responseData: any = null;
        let responseStatus = 500;
        let individualOrderId: string | null = null;

        try {
          // Generate prescription image for this specific product
          const prescriptionImageBase64 =
            await this.prescriptionImageGenerator.generatePrescriptionImage(
              request,
              product,
            );

          prescriptionPayload = this.buildPrescriptionPayload(
            config,
            patientId,
            prescriberId,
            product,
            request,
            prescriptionImageBase64,
          );

          const response = await httpClient.post(
            '/V3/IPS',
            prescriptionPayload,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            },
          );

          responseData = response.data;
          responseStatus = response.status || 200;
          individualOrderId = response.data?.PRESCRIPTIONRXID || null;

          prescriptionResponses.push(responseData);
          if (individualOrderId) {
            prescriptionOrderIds.push(individualOrderId);
          }

          // Log successful individual prescription submission
          await this.logPharmacyIntegration(
            request,
            prescriptionPayload,
            responseData,
            responseStatus,
            individualOrderId,
          );
        } catch (error) {
          // Handle individual prescription error
          if (error.isAxiosError && error.response) {
            responseStatus = error.response.status;
            responseData = error.response.data;
          }

          // Log failed individual prescription submission
          await this.logPharmacyIntegration(
            request,
            prescriptionPayload,
            responseData || error.message,
            responseStatus,
            null,
          );

          // Continue processing other products even if one fails
          this.logger.error(
            `Failed to submit prescription for product ${product.id} to RedRock`,
            error,
          );
        }
      }

      // If all prescriptions failed, throw error
      if (prescriptionOrderIds.length === 0) {
        throw new Error('All prescriptions failed to submit to RedRock');
      }

      const pharmacyOrderIds = prescriptionOrderIds.join(',');

      return {
        success: true,
        orderId: request.treatmentId,
        pharmacyOrderId: pharmacyOrderIds,
        message: 'Prescription submitted successfully to RedRock',
        rawResponse: prescriptionResponses,
      };
    } catch (error) {
      this.logger.error('Failed to submit prescription to RedRock', error);

      if (error.isAxiosError && error.response) {
        const errorMessage = error.response.data?.message || error.message;

        return {
          success: false,
          orderId: request.treatmentId,
          message: `RedRock API error (${error.response.status}): ${errorMessage}`,
          errors: [
            {
              message: `RedRock API error (${error.response.status}): ${errorMessage}`,
            },
          ],
          rawResponse: error.response.data,
        };
      }

      return {
        success: false,
        orderId: request.treatmentId,
        message: `Failed to submit prescription: ${error.message}`,
        errors: [
          { message: `Failed to submit prescription: ${error.message}` },
        ],
      };
    }
  }

  private getRedRockConfig(request: PrescriptionRequest): RedRockConfig {
    const patientState = request.patient.address.state.toUpperCase();
    const isStGeorge = patientState === 'NV' || patientState === 'CA';

    if (isStGeorge) {
      return {
        baseUrl: this.configService.get<string>('REDROCK_STGEORGE_API_URL'),
        storeId: this.configService.get<string>('REDROCK_STGEORGE_STORE_ID'),
        facilityId: this.configService.get<string>(
          'REDROCK_STGEORGE_FACILITY_ID',
        ),
        chargeAccountId: this.configService.get<string>(
          'REDROCK_STGEORGE_CHARGE_ACCOUNT_ID',
        ),
        username: this.configService.get<string>('REDROCK_STGEORGE_USERNAME'),
        password: this.configService.get<string>('REDROCK_STGEORGE_PASSWORD'),
      };
    } else {
      return {
        baseUrl: this.configService.get<string>('REDROCK_SPRINGVILLE_API_URL'),
        storeId: this.configService.get<string>('REDROCK_SPRINGVILLE_STORE_ID'),
        facilityId: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_FACILITY_ID',
        ),
        chargeAccountId: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_CHARGE_ACCOUNT_ID',
        ),
        username: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_USERNAME',
        ),
        password: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_PASSWORD',
        ),
      };
    }
  }

  private async getAuthToken(
    httpClient: AxiosInstance,
    username: string,
    password: string,
  ): Promise<string> {
    try {
      const response = await httpClient.post<string>('/Auth/Authenticate', {
        username,
        password,
      });

      const token = response.data;
      if (!token) {
        throw new Error('No token received from RedRock authentication');
      }

      return token;
    } catch (error) {
      this.logger.error('Failed to authenticate with RedRock', error);
      throw new Error(`RedRock authentication failed: ${error.message}`);
    }
  }

  private async findEntity(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    entityType: string,
    whereCriteria: string,
    idField: string,
  ): Promise<string | null> {
    try {
      const response = await httpClient.get('/V3/IPS', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: { StoreID: storeId, Entity: entityType, WHERE: whereCriteria },
      });

      if (response.data && response.data.length > 0) {
        return response.data[0][idField];
      }

      return null;
    } catch {
      return null;
    }
  }

  private async createEntity(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    entityType: string,
    fields: string[],
    responseIdField: string,
  ): Promise<string> {
    const payload: RedRockEntityPayload = {
      StoreID: storeId,
      Entity: entityType,
      Field: fields,
    };

    const response = await httpClient.post<RedRockEntityResponse>(
      '/V3/IPS',
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );

    const entityId = response.data[responseIdField];
    if (!entityId) {
      throw new Error(
        `Failed to create ${entityType}: No ${responseIdField} in response`,
      );
    }

    return entityId;
  }

  private async getOrCreatePrescriber(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    prescriber: PharmacyPrescriber,
  ): Promise<string> {
    const existingId = await this.findEntity(
      httpClient,
      token,
      storeId,
      'PRESCRIBER',
      `PRESCRIBERNPINUMBER = '${this.urlEncodeSearchValue(prescriber.npi)}'`,
      'PRESCRIBERID',
    );

    if (existingId) {
      return existingId;
    }

    const fields: string[] = [
      `PRESCRIBERFIRSTNAME:${prescriber.firstName}`,
      `PRESCRIBERLASTNAME:${prescriber.lastName}`,
      `PRESCRIBERZIPCODE:19801`,
      `PRESCRIBERCITY:Wilmington`,
      `PRESCRIBERSTATE:DE`,
      `PRESCRIBERNPINUMBER:${prescriber.npi}`,
    ];

    if (prescriber.deaNumber) {
      fields.push(`PRESCRIBERDEALICENSE:${prescriber.deaNumber}`);
    }

    fields.push(`PRESCRIBEROFFICEPHONE1:(*************`);

    return this.createEntity(
      httpClient,
      token,
      storeId,
      'PRESCRIBER',
      fields,
      'PRESCRIBERID',
    );
  }

  private async getOrCreatePatient(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    patient: PharmacyPatient,
    facilityId: string,
    chargeAccountId: string,
  ): Promise<string> {
    const encodedFirstName = this.urlEncodeSearchValue(patient.firstName);
    const encodedLastName = this.urlEncodeSearchValue(patient.lastName);
    const formattedDob = format(new Date(patient.dateOfBirth), 'yyyy/MM/dd');

    const whereCriteria = `PATIENTFIRSTNAME = '${encodedFirstName}' AND PATIENTLASTNAME = '${encodedLastName}' AND PATIENTDOB = '${formattedDob}'`;

    const existingId = await this.findEntity(
      httpClient,
      token,
      storeId,
      'PATIENT',
      whereCriteria,
      'PATIENTID',
    );

    if (existingId) {
      return existingId;
    }

    const fields: string[] = [
      `PATIENTFIRSTNAME:${patient.firstName}`,
      `PATIENTLASTNAME:${patient.lastName}`,
      `PATIENTDOB:${format(new Date(patient.dateOfBirth), 'yyyy/MM/dd')}`,
      `PATIENTZIPCODE:${patient.address.zipCode}`,
      `PATIENTCITY:${patient.address.city}`,
      `PATIENTSTATE:${patient.address.state}`,
      `PATIENTADDRESS:${patient.address.street1}${patient.address.street2 ? ' ' + patient.address.street2 : ''}`,
      `FACILITYID:${facilityId}`,
      `PATIENTDELIVERYFLAG:M`,
      `PATIENTCOURIERID:1`,
      `PRIMARYCHARGEACCOUNTID:${chargeAccountId}`,
      `PATIENTHOMEPHONE:${patient.phoneNumber}`,
      `PATIENTEXTERNALID:${patient.id}`,
      `PATIENTACTIVE:Y`,
    ];

    if (patient.email) {
      fields.push(`PATIENTEMAIL:${patient.email}`);
    }

    if (patient.gender) {
      fields.push(`PATIENTGENDER:${patient.gender.toUpperCase()}`);
    }

    return this.createEntity(
      httpClient,
      token,
      storeId,
      'PATIENT',
      fields,
      'PATIENTID',
    );
  }

  private buildPrescriptionPayload(
    config: RedRockConfig,
    patientId: string,
    prescriberId: string,
    product: PharmacyPrescriptionProduct,
    request: PrescriptionRequest,
    prescriptionImageBase64: string,
  ): RedRockEntityPayload {
    const issueDate =
      request.prescriptionIssueDate || format(new Date(), 'yyyy-MM-dd');
    const formattedDate = format(new Date(issueDate), 'yyyy/MM/dd');

    const fields: string[] = [
      `PRESCRIPTIONTRANDATE:${formattedDate}`,
      `PATIENTID:${patientId}`,
      `PRESCRIBERID:${prescriberId}`,
      `DRUGID:${product.externalId}`,
      `PRESCRIPTIONFILLID:${product.externalId}`,
      `PRESCRIPTONORIGINALQTY:${product.quantity.toString()}`,
      `PRESCRIPTIONFILLQTY:${product.quantity.toString()}`,
      `PRESCRIPTIONSIGCODE:${product.sig}`,
      `PRESCRIPTIONDAYSSUPPLY:${(product.daysSupply || product.quantity * 28).toString()}`,
      `PRESCRIPTIONORIGINALREFILL:${(product.refills || 0).toString()}`,
      `PRESCRIPTIONRECEIVETHROUGH:4`,
      `PRESCRIPTIONTYPEID:O`,
      `PRESCRIPTIONBILL:Y`,
      `DELIVERYSCHEDULE:2`,
      `PRESCRIPTIONPACKINGCODE:6`,
      `PRESCRIPTIONEXTERNALRXID:RX-${request.treatmentId}-${product.id}`,
      `PRESCRIPTIONATTACHMENTBASE64:${prescriptionImageBase64}`,
      `PRESCRIPTIONNOTE:MEDICALLY NECESSARY - patient can't tolerate rapid dose titration and experiences muscle loss; compounding with glycine required to minimize muscle wasting; pharmacy to compound - bill to WILLOW ship to patient`,
    ];

    return {
      StoreID: config.storeId,
      Entity: 'PRESCRIPTION',
      Field: fields,
    };
  }

  private async logPharmacyIntegration(
    request: PrescriptionRequest,
    apiPayload: any,
    responseData: any,
    responseStatus: number,
    orderId: string | null,
  ): Promise<void> {
    try {
      // Get pharmacy ID from slug
      const pharmacy = await this.prisma.pharmacy.findFirst({
        where: { slug: 'redRock' },
      });

      if (!pharmacy) {
        this.logger.warn('Could not find RedRock pharmacy record for logging');
        return;
      }

      // Clone the payload to avoid modifying the original
      const logPayload = JSON.parse(JSON.stringify(apiPayload || {}));

      // Remove base64 image data from Field array for logging
      if (logPayload.Field && Array.isArray(logPayload.Field)) {
        logPayload.Field = logPayload.Field.map((field: string) => {
          if (field.startsWith('PRESCRIPTIONATTACHMENTBASE64:')) {
            return 'PRESCRIPTIONATTACHMENTBASE64:-';
          }
          return field;
        });
      }

      await this.prisma.pharmacyIntegration.create({
        data: {
          pharmacyId: pharmacy.id,
          prescriptionId: request.prescriptionId,
          orderId: orderId || 'NO_ORDER_ID',
          request: logPayload,
          response: responseData || {},
          responseStatus,
        },
      });
    } catch (error) {
      this.logger.error('Failed to log pharmacy integration', error);
      // Don't throw - logging failure shouldn't break the main flow
    }
  }
}
