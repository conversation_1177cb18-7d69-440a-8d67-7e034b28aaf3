import * as path from 'path';
import type {
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import type { CanvasRenderingContext2D } from 'canvas';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createCanvas, registerFont } from 'canvas';
import { format } from 'date-fns';

@Injectable()
export class PrescriptionImageGeneratorService {
  private readonly logger = new Logger(PrescriptionImageGeneratorService.name);
  private readonly resourcesPath: string;

  constructor(private readonly configService: ConfigService) {
    this.resourcesPath = this.configService.get<string>('resourcesPath');

    // Register the custom font
    try {
      const fontPath = path.join(this.resourcesPath, 'Creattion.otf');
      registerFont(fontPath, { family: 'Creattion' });
      this.logger.log('Registered Creattion font for prescriptions');
    } catch (error) {
      this.logger.warn(
        'Failed to register Creattion font, will use fallback',
        error,
      );
    }
  }

  async generatePrescriptionImage(
    request: PrescriptionRequest,
    product: PharmacyPrescriptionProduct,
  ): Promise<string> {
    try {
      // Create canvas with simple prescription format
      const width = 600;
      const height = 700; // Increased height for additional content
      const canvas = createCanvas(width, height);
      const ctx = canvas.getContext('2d');

      // Set background color
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // Draw prescription content
      this.drawSimplePrescription(ctx, request, product, width, height);

      // Convert canvas to base64 string
      return canvas.toBuffer('image/png').toString('base64');
    } catch (error) {
      this.logger.error('Failed to generate prescription image', error);
      throw new Error(`Prescription image generation failed: ${error.message}`);
    }
  }

  private drawSimplePrescription(
    ctx: CanvasRenderingContext2D,
    request: PrescriptionRequest,
    product: PharmacyPrescriptionProduct,
    width: number,
    height: number,
  ) {
    const margin = 20;
    const fieldHeight = 30;
    let y = margin;

    // Draw border
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, width, height);

    // Set font for all text
    ctx.fillStyle = '#000000';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';

    // To Red Rock Pharmacy (top center)
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('To Red Rock Pharmacy', width / 2, y + 20);
    y += 40; // Add spacing after title

    // Prescribed Date (top right)
    const prescribedDate = request.prescriptionIssueDate
      ? format(new Date(request.prescriptionIssueDate), 'MM/dd/yyyy')
      : format(new Date(), 'MM/dd/yyyy');

    ctx.font = '16px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(`Prescribed Date: ${prescribedDate}`, width - margin, y + 20);

    // Reset alignment and font
    ctx.textAlign = 'left';
    ctx.font = '16px Arial';
    y += fieldHeight + 15;

    // Patient Name
    ctx.fillText(
      `Patient Name: ${request.patient.firstName} ${request.patient.lastName}`,
      margin,
      y,
    );
    y += fieldHeight;

    // Date of Birth
    const dob = format(new Date(request.patient.dateOfBirth), 'MM/dd/yyyy');
    ctx.fillText(`Date of Birth: ${dob}`, margin, y);
    y += fieldHeight;

    // Patient Address
    const address = `${request.patient.address.street1}${request.patient.address.street2 ? ', ' + request.patient.address.street2 : ''}, ${request.patient.address.city}, ${request.patient.address.state} ${request.patient.address.zipCode}`;
    ctx.fillText(`Address: ${address}`, margin, y);
    y += fieldHeight + 10;

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 20;

    // Drug Name
    ctx.fillText(`Drug: ${product.name || '<Drug Name>'}`, margin, y);
    y += fieldHeight;

    // Directions
    const directions = product.sig || '<Enter Directions here>';
    // Split long directions into multiple lines if needed
    const maxWidth = width - margin * 2;
    const directionLines = this.wrapText(ctx, directions, maxWidth);
    ctx.fillText(`Directions: ${directionLines[0]}`, margin, y);
    y += fieldHeight;

    // Additional direction lines if needed
    for (let i = 1; i < directionLines.length; i++) {
      ctx.fillText(`           ${directionLines[i]}`, margin, y);
      y += fieldHeight;
    }

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 20;

    // Quantity and Refills (on same line)
    const quantity = product.quantity || 1;
    const refills = '0';

    ctx.fillText(`Quantity: ${quantity}`, margin, y);
    ctx.fillText(`Refills: ${refills}`, width - 150, y);
    y += fieldHeight + 10;

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 20;

    // Medical Reasoning
    ctx.font = 'bold 16px Arial';
    ctx.fillText('Medical Reasoning:', margin, y);
    y += fieldHeight;

    ctx.font = '14px Arial';
    const medicalReasoning =
      "MEDICALLY NECESSARY - patient can't tolerate rapid dose titration and experiences muscle loss; compounding with glycine required to minimize muscle wasting; pharmacy to compound - bill to WILLOW ship to patient";
    const medicalReasoningLines = this.wrapText(
      ctx,
      medicalReasoning,
      maxWidth,
    );
    for (const line of medicalReasoningLines) {
      ctx.fillText(line, margin, y);
      y += 20;
    }

    // Horizontal line separator
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, y);
    ctx.lineTo(width - margin, y);
    ctx.stroke();
    y += 50;

    // Prescriber Name
    ctx.font = '16px Arial';
    ctx.fillText(
      `Prescriber Name: ${request.prescriber.firstName} ${request.prescriber.lastName}`,
      margin,
      y,
    );
    y += fieldHeight;

    // Prescriber NPI
    ctx.fillText(`Prescriber NPI: ${request.prescriber.npi}`, margin, y);
    y += fieldHeight + 20;

    // E-Signature (using custom Creattion font)
    try {
      ctx.font = '36px Creattion'; // Increased by 50% from 24px
    } catch {
      // Fallback to italic Times New Roman if custom font fails
      ctx.font = 'italic 36px Times New Roman';
    }
    ctx.fillText(
      `Dr. ${request.prescriber.firstName} ${request.prescriber.lastName}`,
      margin,
      y,
    );
    y += 40; // Increased spacing to accommodate larger signature

    // Prescriber Signature label
    ctx.font = '14px Arial';
    ctx.fillText('Prescriber Signature', margin, y);
    y += fieldHeight;

    // Date Signed (bottom right)
    const dateSigned = request.prescriptionIssueDate
      ? format(new Date(request.prescriptionIssueDate), 'MM/dd/yyyy')
      : format(new Date(), 'MM/dd/yyyy');

    ctx.textAlign = 'right';
    ctx.fillText(`Date Signed: ${dateSigned}`, width - margin, y);
  }

  private wrapText(
    ctx: CanvasRenderingContext2D,
    text: string,
    maxWidth: number,
  ): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;
      const metrics = ctx.measureText(testLine);

      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }
}
