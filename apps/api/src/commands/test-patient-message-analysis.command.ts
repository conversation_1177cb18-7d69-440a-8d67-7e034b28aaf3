import { PatientMessageRouterService } from '@/modules/chat/services/patient-message-router.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { GoogleSheetsService } from '@/modules/shared/services/google-sheets.service';
import { Injectable } from '@nestjs/common';
import { formatInTimeZone } from 'date-fns-tz';
import { Command, CommandRunner, Option } from 'nest-commander';

interface MessageData {
  user: string;
  message: string;
  date: string;
  id?: string;
}

@Injectable()
@Command({
  name: 'test-patient-message-analysis',
  description: `Test the updated patient message analysis prompt against existing conversation router records
    View results in: https://docs.google.com/spreadsheets/d/1i_qw_Wz1_dodoGi7Tq0zG_e5xhgn0z068eTjuPgiCPI/edit
    `,
})
export class TestPatientMessageAnalysisCommand extends CommandRunner {
  private readonly TIMEZONE = 'America/New_York';

  constructor(
    private readonly prisma: PrismaService,
    private readonly patientMessageRouter: PatientMessageRouterService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super();
  }

  @Option({
    flags: '--limit [number]',
    description: 'Limit number of records to process (default: 10)',
  })
  parseLimit(val: string): number {
    const num = parseInt(val, 10);
    if (isNaN(num) || num <= 0) {
      throw new Error('Limit must be a positive number');
    }
    return num;
  }

  @Option({
    flags: '--skip [number]',
    description: 'Skip number of records (default: 0)',
  })
  parseSkip(val: string): number {
    const num = parseInt(val, 10);
    if (isNaN(num) || num < 0) {
      throw new Error('Skip must be a non-negative number');
    }
    return num;
  }

  @Option({
    flags: '--ids [ids]',
    description:
      'Comma-separated list of ConversationRouter IDs to process (overrides limit/skip)',
  })
  parseIds(val: string): string[] {
    return val
      .split(',')
      .map((id) => id.trim())
      .filter((id) => id.length > 0);
  }

  async run(
    _: string[],
    options?: { limit?: number; skip?: number; ids?: string[] },
  ): Promise<void> {
    if (options?.ids && options.ids.length > 0) {
      console.log(
        `Processing specific conversation router IDs: ${options.ids.join(', ')}`,
      );

      // Find specific conversation router records by IDs
      const conversationRouters = await this.prisma.conversationRouter.findMany(
        {
          where: {
            id: { in: options.ids },
            status: { in: ['processed', 'closed'] },
            messages: { not: { equals: [] } },
          },
          include: {
            conversation: {
              include: {
                patient: { include: { doctor: { include: { user: true } } } },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      );

      await this.processRouters(conversationRouters);
      return;
    }

    const limit = options?.limit || 10;
    const skip = options?.skip || 0;

    console.log(
      `Processing ${limit} conversation router records (skipping ${skip})...`,
    );

    // Find completed conversation router records
    const conversationRouters = await this.prisma.conversationRouter.findMany({
      where: {
        status: { in: ['processed', 'closed'] },
        messages: { not: { equals: [] } },
      },
      include: {
        conversation: {
          include: {
            patient: { include: { doctor: { include: { user: true } } } },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    });

    await this.processRouters(conversationRouters);
  }

  private async processRouters(conversationRouters: any[]): Promise<void> {
    if (conversationRouters.length === 0) {
      console.log('No completed conversation router records found');
      return;
    }

    console.log(`Found ${conversationRouters.length} records to process`);

    for (const router of conversationRouters) {
      try {
        await this.processConversationRouter(router);
      } catch (error) {
        console.error(
          `Failed to process conversation router ${router.id}: ${error.message}`,
          error.stack,
        );
      }
    }

    console.log('Completed processing all records');
  }

  private async processConversationRouter(router: any): Promise<void> {
    console.log(`Processing conversation router ${router.id}`);

    // The router.messages contains message IDs, not the actual messages
    const messageIds = router.messages || [];
    if (messageIds.length === 0) {
      console.warn(`No message IDs found for conversation router ${router.id}`);
      return;
    }

    // Fetch the actual conversation messages using the IDs
    const conversationMessages = await this.prisma.conversationMessage.findMany(
      {
        where: {
          id: { in: messageIds },
        },
        include: {
          user: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      },
    );

    if (conversationMessages.length === 0) {
      console.warn(`No conversation messages found for router ${router.id}`);
      return;
    }

    // Convert to MessageData format expected by the analysis
    const originalMessages: MessageData[] = conversationMessages.map((msg) => ({
      user:
        msg.userId === router.conversation.patient.doctor.userId
          ? 'doctor'
          : 'patient',
      message: msg.content || '',
      date: msg.createdAt.toISOString(),
      id: msg.id,
    }));

    // Get original analysis data from the router
    const originalTarget = {
      messageSummary: router.reason || '',
      relevantForPatientServices: router.relevantForPatientServices || false,
      relevantForDoctor: router.relevantForDoctor || false,
      inquiryTypes: router.inquiryTypes || [],
    };

    // Get new analysis using the updated prompt
    const newAnalysis =
      await this.patientMessageRouter.analyzePatientMessages(originalMessages);

    // Format original messages for Google Sheets (join with double newlines)
    const formattedOriginalMessages = originalMessages
      .map((msg) => `[${msg.user}] ${msg.message}`)
      .join('\n\n');

    // Prepare data for Google Sheets
    const sheetData = {
      originalMessages: formattedOriginalMessages,
      originalSummary: originalTarget.messageSummary || '',
      originalRelevantForPS: originalTarget.relevantForPatientServices || false,
      originalRelevantForDoctor: originalTarget.relevantForDoctor || false,
      newSummary: newAnalysis.messageSummary,
      newRelevantForPS: newAnalysis.relevantForPatientServices,
      newRelevantForDoctor: newAnalysis.relevantForDoctor,
      date: formatInTimeZone(
        new Date(router.createdAt as string),
        this.TIMEZONE,
        'MM/dd/yyyy HH:mm:ss',
      ),
    };

    // Send to Google Sheets
    await this.googleSheetsService.appendAnalysisRecord(sheetData);

    console.log(`Successfully processed conversation router ${router.id}`);
  }
}
