import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

// Type for pharmacy data
export interface Pharmacy {
  id: string;
  name: string;
  slug?: string;
  color?: string;
  enabled: boolean;
  regularPriority?: number;
  usingGLP1Priority?: number;
  PharmacyOnState?: {
    pharmacyId: string;
    stateId: string;
    state: {
      id: string;
      code: string;
      name: string;
      enabled?: boolean;
    };
  }[];
  patientCount?: number;
}

// Type for state patient counts
export interface StatePatientCount {
  stateId: string;
  stateName: string;
  stateCode: string;
  patientCount: number;
  enabled: boolean;
  isAssociated?: boolean; // Whether the state is currently associated with the pharmacy
}

// Response type for patient counts by state
export interface PharmacyPatientCountsResponse {
  statePatientCounts: StatePatientCount[];
  totalPatientCount: number;
}

export interface PharmaciesResponse {
  pharmacies: Pharmacy[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Get all enabled pharmacies
export const useGetEnabledPharmacies = () => {
  return useQuery<Pharmacy[]>({
    queryKey: ['pharmacies', 'enabled'],
    queryFn: async () => {
      const { data } = await apiClient.get<PharmaciesResponse>('/pharmacy', {
        params: {
          showInactive: false,
        },
      });
      // Log pharmacy data to debug
      console.log('Enabled pharmacies data:', JSON.stringify(data.pharmacies));
      return data.pharmacies || [];
    },
  });
};
export const useGetAvailablePharmaciesInState = (stateId: string) => {
  return useQuery<Pharmacy[]>({
    queryKey: ['pharmacies', stateId, 'enabled'],
    queryFn: async () => {
      const { data } = await apiClient.get<PharmaciesResponse>('/pharmacy', {
        params: {
          showInactive: false,
          inStateId: stateId,
        },
      });
      return data.pharmacies || [];
    },
  });
};

// Get all product forms available in a pharmacy
export const useGetPharmacyProductForms = (pharmacyId: string) => {
  return useQuery<string[]>({
    queryKey: ['pharmacy', pharmacyId, 'product-forms'],
    queryFn: async () => {
      if (!pharmacyId) return [];

      // Get pharmacy details including products
      const { data } = await apiClient.get(`/pharmacy/${pharmacyId}`);

      // Extract unique forms from products
      const forms = new Set<string>();

      if (data.Product && Array.isArray(data.Product)) {
        data.Product.forEach((product: { form?: string }) => {
          if (product.form) {
            forms.add(product.form);
          }
        });
      }

      return Array.from(forms);
    },
    enabled: !!pharmacyId,
  });
};

// Get product forms for both source and target pharmacies
export const useGetPharmaciesProductForms = (
  sourcePharmacyId: string,
  targetPharmacyId: string,
) => {
  const sourceFormsQuery = useGetPharmacyProductForms(sourcePharmacyId);
  const targetFormsQuery = useGetPharmacyProductForms(targetPharmacyId);

  return {
    sourceForms: sourceFormsQuery.data || [],
    targetForms: targetFormsQuery.data || [],
    isLoading: sourceFormsQuery.isLoading || targetFormsQuery.isLoading,
    isError: sourceFormsQuery.isError || targetFormsQuery.isError,
  };
};

// Hook to get patient counts by state for a pharmacy
export const useGetPharmacyPatientCountsByState = (pharmacyId: string) => {
  return useQuery<PharmacyPatientCountsResponse>({
    queryKey: ['pharmacy', pharmacyId, 'patient-counts-by-state'],
    queryFn: async () => {
      const { data } = await apiClient.get<PharmacyPatientCountsResponse>(
        `/pharmacy/${pharmacyId}/patient-counts-by-state`,
      );
      return data;
    },
    // Only fetch when pharmacyId is provided
    enabled: !!pharmacyId,
  });
};

// Hook to transfer a patient to a new pharmacy
export const useTransferPatientToPharmacy = (patientId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ newPharmacyId }: { newPharmacyId: string }) => {
      const { data } = await apiClient.post('/pharmacy/transfer-patient', {
        patientId,
        newPharmacyId,
      });
      return data;
    },
    onSuccess: () => {
      // Invalidate all patient-related queries to ensure UI is updated
      void queryClient.invalidateQueries({ queryKey: ['patient', patientId] });
      void queryClient.invalidateQueries({ queryKey: ['patients', patientId] });

      // Also invalidate treatments since they might be affected by pharmacy change
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });

      // Invalidate the patients list if it exists
      void queryClient.invalidateQueries({ queryKey: ['patients'] });
    },
  });
};
