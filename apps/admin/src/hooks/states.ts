import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface State {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
}

interface StatesResponse {
  states: State[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * Hook for fetching states with optional pagination support
 * @param params Pagination and filtering parameters (defaults to page 1, limit 50)
 */
export function useGetStates(params?: FetchParams) {
  // Default to page 1, limit 50 if no params provided
  const queryParams = params || { page: 1, limit: 50 };

  return useQuery<StatesResponse>({
    queryKey: ['states', queryParams],
    queryFn: async () => {
      const { data } = await apiClient.get('/state', { params: queryParams });
      return data;
    },
  });
}

export function useToggleStateEnabled() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, enabled }: { id: string; enabled: boolean }) => {
      const response = await apiClient.patch<State>(`/state/${id}`, {
        enabled,
      });
      return response.data;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['states'] });
    },
  });
}

/**
 * Hook to get all enabled states
 * This is useful for components that need access to all states without pagination
 */
export function useGetAllEnabledStates() {
  return useQuery<State[]>({
    queryKey: ['all-enabled-states'],
    queryFn: async () => {
      const { data } = await apiClient.get('/state', {
        params: {
          limit: 100, // Large enough to get all states
        },
      });
      // Filter to only include enabled states
      return data.states.filter((state: State) => state.enabled);
    },
  });
}
