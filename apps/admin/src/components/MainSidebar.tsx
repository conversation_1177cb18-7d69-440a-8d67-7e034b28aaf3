import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { Capability } from '@willow/auth';

import ActivityIcon from '~/assets/svg/activity.svg';
import AdminIcon from '~/assets/svg/admin.svg';
import DoctorIcon from '~/assets/svg/doctor.svg';
import HomeIcon from '~/assets/svg/home.svg';
import Logo from '~/assets/svg/logo.svg';
import PharmacyIcon from '~/assets/svg/pharmacies.svg';
import ProductIcon from '~/assets/svg/product.svg';
import StateIcon from '~/assets/svg/state.svg';
import { WithCapability } from '~/components/capability';
import { useProfile } from '~/hooks/useProfile';

// Custom styles for SVG icons
const navIconStyles = `
  .nav-icon svg path {
    stroke: #94A3B8;
    transition: stroke 0.2s ease;
  }

  .nav-icon.active svg path,
  .nav-icon:hover svg path {
    stroke: white;
  }
`;

interface NavigationItem {
  href: string;
  Icon: any;
  label: string;
  enabled: boolean;
  requiredCapabilities: Capability[];
  // Array of paths that should activate this navigation item
  activePaths?: string[];
}

const navigationItems: NavigationItem[] = [
  {
    href: '/',
    Icon: HomeIcon,
    label: 'Home',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_DASHBOARD],
    activePaths: ['/patients/'],
  },
  {
    href: '/doctors',
    Icon: DoctorIcon,
    label: 'Doctors',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_DOCTORS],
    activePaths: ['/doctors'],
  },
  {
    href: '/admins',
    Icon: AdminIcon,
    label: 'Admins',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_ADMINS],
    activePaths: ['/admins'],
  },
  {
    href: '/pharmacies',
    Icon: PharmacyIcon,
    label: 'Pharmacies',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_PHARMACIES],
    activePaths: ['/pharmacies'],
  },
  {
    href: '/states',
    Icon: StateIcon,
    label: 'States',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_STATES],
    activePaths: ['/states'],
  },
  {
    href: '/products',
    Icon: ProductIcon,
    label: 'Products',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_PRODUCTS],
    activePaths: ['/products', '/product-prices'],
  },
  {
    href: '/insights/queues',
    Icon: ActivityIcon,
    label: 'Insights',
    enabled: true,
    requiredCapabilities: [Capability.VIEW_INSIGHTS],
    activePaths: ['/insights', 'insights/queues', 'insights/queues/messages'],
  },
];

export const MainSidebar = () => {
  const pathname = usePathname();
  const profile = useProfile();

  /**
   * Determines if a navigation item is active based on the current pathname
   * @param item The navigation item to check
   * @returns boolean indicating if the item should be shown as active
   */
  const isItemActive = (item: NavigationItem): boolean => {
    // Check if the current path exactly matches the href
    if (pathname === item.href) {
      return true;
    }

    // Check if the current path starts with the href (for nested routes)
    // But only if the href is not the root path
    if (item.href !== '/' && pathname.startsWith(item.href)) {
      return true;
    }

    // Check against the activePaths array if provided
    if (item.activePaths && item.activePaths.length > 0) {
      // Check for exact path matches
      if (item.activePaths.includes(pathname)) {
        return true;
      }

      // Check for path prefix matches
      for (const activePath of item.activePaths) {
        if (activePath.endsWith('/') && pathname.startsWith(activePath)) {
          return true;
        }
      }
    }

    return false;
  };

  return (
    <div className="font-neue h-screen w-20 bg-denim py-9">
      <style dangerouslySetInnerHTML={{ __html: navIconStyles }} />
      <Link className="flex justify-center" href="/">
        <Logo />
      </Link>
      <div className="scrollbar-hidden h-full space-y-4 overflow-y-scroll pt-20">
        {navigationItems.map((item) => {
          const isActive = isItemActive(item);

          const navItem = item.enabled ? (
            <Link
              key={item.href}
              className={`nav-icon relative flex h-10 w-full cursor-pointer items-center justify-center hover:bg-denim-light ${isActive ? 'active' : ''}`}
              href={item.href}
              aria-label={item.label}
              title={item.label}
            >
              {isActive && (
                <div className="absolute left-0 top-0 h-full w-[11px] bg-electric" />
              )}
              <div>
                <item.Icon />
              </div>
            </Link>
          ) : (
            <div
              key={item.href}
              className="nav-icon relative flex h-10 w-full cursor-not-allowed items-center justify-center opacity-50"
              aria-label={item.label}
              title={`${item.label} (Coming Soon)`}
            >
              <div>
                <item.Icon />
              </div>
            </div>
          );

          // Wrap each navigation item with the WithCapability component
          return (
            <WithCapability
              key={item.href}
              requiredCapabilities={item.requiredCapabilities}
              hideWhenDisabled={true}
            >
              {navItem}
            </WithCapability>
          );
        })}
      </div>
    </div>
  );
};
