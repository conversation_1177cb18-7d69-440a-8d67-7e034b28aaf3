import React, { useCallback, useState } from 'react';
import { intlFormat } from 'date-fns';
import {
  ArrowDownIcon,
  ArrowDownUpIcon,
  ArrowUpIcon,
  CircleEllipsisIcon,
  InfoIcon,
  RefreshCcw,
} from 'lucide-react';

import type { TreatmentItemAPI } from '@willow/db';
import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@willow/ui/base/tooltip';
import { formatDateToLocal } from '@willow/utils/format';
import { billingCycleToDisplay } from '@willow/utils/treatment';

import { Chip } from '~/components/Chip';
import { CancelPrescriptionDialog } from './CancelPrescriptionDialog';
import { FireNextConfirmationDialog } from './FireNextConfirmationDialog';
import { MoveRefillDateButton } from './MoveRefillDateButton';
import { PauseConfirmationDialog } from './PauseConfirmationDialog';
import { RetryPaymentConfirmationDialog } from './RetryPaymentConfirmationDialog';

const statusToStyle = {
  scheduled: {
    label: 'Scheduled',
    bgColor: 'bg-treatment-scheduled',
    textColor: 'text-treatment-scheduled',
  },
  'inProgress.readyToCharge': {
    label: 'Waiting',
    bgColor: 'bg-treatment-waiting',
    textColor: 'text-treatment-waiting',
  },
  'inProgress.charging': {
    label: 'Charging',
    bgColor: 'bg-treatment-charging',
    textColor: 'text-treatment-charging',
  },
  'inProgress.waitingForPrescription': {
    label: 'Waiting for Prescription',
    bgColor: 'bg-treatment-prescribe',
    textColor: 'text-treatment-prescribe',
  },
  'inProgress.waitingBetweenRefills': {
    label: 'Prescribed',
    bgColor: 'bg-treatment-prescribed',
    textColor: 'text-treatment-prescribed',
  },
  paused: {
    label: 'Paused',
    bgColor: 'bg-treatment-paused',
    textColor: 'text-treatment-paused',
  },
  failed: {
    label: 'Payment Failed',
    bgColor: 'bg-treatment-paymentFailed',
    textColor: 'text-treatment-paymentFailed',
  },
  cancelled: {
    label: 'Canceled',
    bgColor: 'bg-treatment-canceled',
    textColor: 'text-treatment-canceled',
  },
  uncollectible: {
    label: 'Uncollectible',
    bgColor: 'bg-treatment-uncollectible',
    textColor: 'text-treatment-uncollectible',
  },
  completed: {
    label: 'Complete',
    bgColor: 'bg-treatment-completed',
    textColor: 'text-treatment-completed',
  },
  transferred: {
    label: 'Transferred',
    bgColor: 'bg-treatment-transferred',
    textColor: 'text-treatment-transferred',
  },
};
type StatusToStyleKey = keyof typeof statusToStyle;

const formatDate = (date: string) => {
  return intlFormat(new Date(date), {
    year: 'numeric',
    month: 'long',
    day: '2-digit',
  });
};

export const PresctiptionCard = ({
  patientId,
  treatment,
  onClick,
  isSelected,
  handlePrescriptionHistoryClick,
}: {
  patientId: string;
  treatment: TreatmentItemAPI;
  onClick?: (value: { patientId: string; treatment: TreatmentItemAPI }) => void;
  isSelected?: boolean;
  handlePrescriptionHistoryClick?: () => void;
}) => {
  const showNextRefill =
    treatment.currentRefill < treatment.refills &&
    !['cancelled', 'uncollectible', 'completed'].includes(treatment.state);

  const [openCancelPrescriptionDialog, setOpenCancelPrescriptionDialog] =
    useState(false);
  const handleCancelConfimationDialogOpen = useCallback(() => {
    setOpenCancelPrescriptionDialog(true);
  }, []);

  const [isPauseConfirmationOpen, setIsPauseConfirmationOpen] = useState(false);
  const handlePauseConfimationDialogOpen = useCallback(() => {
    setIsPauseConfirmationOpen(true);
  }, []);
  const [isFireNextConfirmationOpen, setIsFireNextConfirmationOpen] =
    useState(false);
  const handleFireNextConfimationDialogOpen = useCallback(() => {
    setIsFireNextConfirmationOpen(true);
  }, []);

  const [isRetryPaymentConfirmationOpen, setisRetryPaymentConfirmationOpen] =
    useState(false);
  const handleOpenRetryPaymentConfirmationDialog = useCallback(() => {
    setisRetryPaymentConfirmationOpen(true);
  }, []);

  const statusStyle = statusToStyle[treatment.state as StatusToStyleKey];

  return (
    <div
      className={cn(
        'flex min-h-[160px] w-full flex-col justify-between gap-4 rounded-lg border border-denim/30 bg-stone-light/50 p-4',
        { 'border-[1.50px] border-denim': isSelected },
      )}
    >
      <div className="flex flex-row justify-between">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-x-2">
            <button
              className={cn(
                'text-xl font-medium text-dark',
                onClick ? 'hover:underline' : '',
              )}
              disabled={!onClick}
              onClick={() => onClick?.({ patientId, treatment })}
            >
              {treatment.activeProduct.name} | {treatment.activeProduct.dose}mg
            </button>
          </div>

          <div className={cn('text-xs font-normal', statusStyle.textColor)}>
            {`${treatment.refills - treatment.currentRefill} out of ${treatment.refills} Refills Left`}
          </div>
          <div>
            <Chip
              className={cn(
                'fle w-max items-center bg-denim px-3 pb-0.5 pt-1 text-sm text-denim-foreground text-white',
                statusStyle.bgColor,
              )}
            >
              {statusStyle?.label === 'Paused' && treatment.nextEventIn ? (
                <span>
                  Paused until{' '}
                  {formatDateToLocal(new Date(treatment.nextEventIn))}
                </span>
              ) : (
                statusStyle.label
              )}
            </Chip>
          </div>
        </div>

        <div
          className={cn(
            'flex flex-col items-end gap-2 text-xs font-normal text-denim',
          )}
        >
          <div className="flex items-center gap-1">
            {treatment.refillSystem === 'scaling' && <ArrowUpIcon size={20} />}
            {treatment.refillSystem === 'downscaling' && (
              <ArrowDownIcon size={20} />
            )}
            {treatment.refillSystem === 'static' && (
              <ArrowDownUpIcon size={20} />
            )}
            <span>{treatment.activeProduct.form}</span>
          </div>
          <div className="flex items-center gap-1">
            <RefreshCcw width={16} height={16} />
            <span>{billingCycleToDisplay(treatment.vials)}</span>
          </div>
          <div className="flex items-center gap-1">
            <span> {treatment.activeProduct.pharmacy}</span>
          </div>
        </div>
      </div>
      <div className="flex flex-row items-end justify-between">
        <div className="flex flex-row gap-4">
          <div>
            <div className="text-[11px] font-normal text-stone/60">
              Prescribed
            </div>
            <div className="text-[13px] font-normal text-dark">
              {['scheduled'].includes(treatment.state)
                ? '-'
                : treatment.inProgressSince
                  ? formatDate(treatment.inProgressSince)
                  : '-'}
            </div>
          </div>
          <div>
            <div className="text-[11px] font-normal text-stone/60">
              Last Refill Ends
            </div>
            <div className="text-[13px] font-normal text-dark">
              {['cancelled', 'uncollectible'].includes(treatment.state)
                ? '-'
                : treatment.endOfLastRefillDate
                  ? formatDate(treatment.endOfLastRefillDate)
                  : '-'}
            </div>
          </div>
          <div>
            <div className="text-[11px] font-normal text-stone/60">
              Next Refill
            </div>
            <div className="flex items-center gap-2">
              <div className="text-[13px] font-normal text-dark">
                {showNextRefill && treatment.nextRefillDate
                  ? formatDate(treatment.nextRefillDate)
                  : '-'}
              </div>
              {treatment.allowedActions.includes('moveRefillDate') && (
                <MoveRefillDateButton
                  treatmentId={treatment.treatmentId}
                  patientId={patientId}
                  nextRefillDate={treatment.nextRefillDate}
                />
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-row gap-1 pb-1 text-denim">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger className="cursor-pointer" asChild>
                <div onClick={() => handlePrescriptionHistoryClick?.()}>
                  <InfoIcon height={18} width={18} />
                </div>
              </TooltipTrigger>
              <TooltipContent className="rounded-lg bg-slate-950 p-2 text-white">
                <p>Prescription history</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="cursor-pointer">
                <CircleEllipsisIcon height={18} width={18} />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="my-2 w-36 px-0"
              align="end"
              sideOffset={10}
            >
              <DropdownMenuItem
                disabled={!treatment.allowedActions.includes('pause')}
                className="hover:bg-yellow focus:bg-yellow cursor-pointer rounded-none px-3 py-2 text-xs font-normal text-dark hover:text-denim"
                onSelect={handlePauseConfimationDialogOpen}
              >
                Pause Prescription
              </DropdownMenuItem>
              {treatment.vials === 1 && (
                <DropdownMenuItem
                  disabled={!treatment.allowedActions.includes('fireNext')}
                  className="hover:bg-yellow focus:bg-yellow cursor-pointer rounded-none px-3 py-2 text-xs font-normal text-dark hover:text-denim"
                  onSelect={handleFireNextConfimationDialogOpen}
                >
                  Fire Next Order
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                disabled={treatment.state !== 'failed'}
                className="hover:bg-yellow focus:bg-yellow cursor-pointer rounded-none px-3 py-2 text-xs font-normal text-dark hover:text-denim"
                onSelect={handleOpenRetryPaymentConfirmationDialog}
              >
                Retry Charge
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={!treatment.allowedActions.includes('cancel')}
                className="hover:bg-yellow focus:bg-yellow cursor-pointer rounded-none px-3 py-2 text-xs font-normal text-error"
                onSelect={handleCancelConfimationDialogOpen}
              >
                Cancel Prescription
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <CancelPrescriptionDialog
            open={openCancelPrescriptionDialog}
            onClose={setOpenCancelPrescriptionDialog}
            treatmentId={treatment.treatmentId}
            patientId={patientId}
          />
          <PauseConfirmationDialog
            open={isPauseConfirmationOpen}
            setOpen={setIsPauseConfirmationOpen}
            treatmentId={treatment.treatmentId}
            patientId={patientId}
          />
          <FireNextConfirmationDialog
            open={isFireNextConfirmationOpen}
            setOpen={setIsFireNextConfirmationOpen}
            treatmentId={treatment.treatmentId}
            patientId={patientId}
          />
          {isRetryPaymentConfirmationOpen && (
            <RetryPaymentConfirmationDialog
              setOpen={setisRetryPaymentConfirmationOpen}
              treatmentId={treatment.treatmentId}
              state={treatment.state}
              patientId={patientId}
            />
          )}
        </div>
      </div>
    </div>
  );
};
