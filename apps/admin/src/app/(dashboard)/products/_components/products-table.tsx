'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import Link from 'next/link';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { ListedProduct } from '~/hooks/products';
import { env } from '~/env';
import { useGetProducts } from '~/hooks/products';
import { ProductInfo } from './product-info';

export function ProductsTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState();

  const [selectedProductId, setSelectedProductId] = useQueryState('productId', {
    defaultValue: '',
  });

  const [duplicateId] = useQueryState('duplicate');

  // State for showing inactive products - show both active and inactive by default
  const [showInactive, setShowInactive] = useState(true);

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
      // API filtering is handled client-side, so no need to pass showInactive
    }),
    [query, pagination, sorting],
  );

  const { data, isPending, isError } = useGetProducts(fetchParams);

  // Determine Stripe product URL base on environment
  const stripeBaseUrl = useMemo(() => {
    return env.NEXT_PUBLIC_ENVIRONMENT === 'production'
      ? 'https://dashboard.stripe.com/products/'
      : 'https://dashboard.stripe.com/test/products/';
  }, []);

  const columns: ColumnDef<ListedProduct>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="text-sm text-denim">
            {row.original.label}, {row.original.form}
          </div>
        ),
      },
      {
        accessorKey: 'pharmacy',
        header: () => <ColumnHeader label="Pharmacy" sortKey="pharmacy" />,
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {row.original.pharmacy ? (
              <Link
                href={`/pharmacies/all?pharmacyId=${row.original.pharmacy.id}`}
                className="text-denim hover:underline"
                onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
              >
                {row.original.pharmacy.name}
              </Link>
            ) : (
              'Not Set'
            )}
          </div>
        ),
      },
      {
        accessorKey: 'defaultPrice',
        header: 'Default Price',
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {row.original.defaultPrice ? (
              <Link
                href={`/product-prices?productId=${row.original.id}&priceId=${row.original.defaultPrice.id}`}
                className="text-denim hover:underline"
                onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
              >
                {row.original.defaultPrice.dosageLabel || 'Default Price'}
              </Link>
            ) : (
              'No price set'
            )}
          </div>
        ),
      },
      {
        accessorKey: 'id',
        header: 'Stripe',
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            <a
              href={`${stripeBaseUrl}${row.original.id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-denim hover:underline"
              onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
            >
              product
            </a>
          </div>
        ),
      },
      {
        accessorKey: 'active',
        header: () => <ColumnHeader label="Status" sortKey="active" />,
        cell: ({ row }) => {
          const isActive = row.original.active;
          return (
            <div className="text-sm">
              {isActive ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Inactive
                </span>
              )}
            </div>
          );
        },
      },
      {
        id: 'prices',
        header: 'Prices',
        cell: ({ row }) => (
          <div className="text-sm">
            <Link
              href={`/product-prices?productId=${row.original.id}`}
              className="text-denim hover:underline"
              onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
            >
              Manage Prices
            </Link>
          </div>
        ),
      },
    ],
    [stripeBaseUrl],
  );

  // Process the products data - filter by active status
  const products = useMemo(() => {
    const allProducts = data?.products || [];

    // If showInactive is true, show all products
    // If showInactive is false, show only active products
    if (!showInactive) {
      return allProducts.filter((product) => product.active);
    }

    return allProducts;
  }, [data, showInactive]);

  const table = useReactTable({
    data: products,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination?.totalPages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">Products</div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search products"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedProductId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedProductId('');
          }}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedProductId(row.original.id);
                        }}
                      >
                        <TableRow
                          data-state={row.getIsSelected() && 'selected'}
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError ? 'Error loading products.' : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>
          {selectedProductId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[800px] !touch-none !select-text">
                <DrawerTitle className="hidden">
                  Product Information
                </DrawerTitle>
                <ProductInfo
                  productId={selectedProductId}
                  handleClose={() => {
                    void setSelectedProductId('');
                    // Clear the duplicate parameter if it exists
                    if (duplicateId) {
                      // Create a new URL without the duplicate parameter
                      const url = new URL(window.location.href);
                      url.searchParams.delete('duplicate');
                      window.history.replaceState({}, '', url.toString());
                    }
                  }}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <Button onClick={() => setSelectedProductId('new')}>
            Add New Product
          </Button>
          <Button
            variant="electric"
            onClick={() => {
              setShowInactive(!showInactive);
              void setPagination({ page: 1 }); // Reset to first page when toggling
            }}
          >
            {showInactive ? 'Show Active Products Only' : 'Show All Products'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>
    </div>
  );
}
