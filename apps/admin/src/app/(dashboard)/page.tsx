'use client';

import { useEffect } from 'react';
import { redirect, useRouter } from 'next/navigation';

import { Capability } from '@willow/auth';

import { WithCapability } from '~/components/capability';
import { useCapabilities } from '~/hooks/useCapabilities';

// Navigation items with their required capabilities
const navigationSections = [
  { path: '/patients', capability: Capability.VIEW_PATIENTS },
  { path: '/doctors', capability: Capability.VIEW_DOCTORS },
  { path: '/pharmacies', capability: Capability.VIEW_PHARMACIES },
  { path: '/products', capability: Capability.VIEW_PRODUCTS },
  { path: '/admins', capability: Capability.VIEW_ADMINS },
  { path: '/states', capability: Capability.VIEW_STATES },
];

export default function HomePage() {
  const router = useRouter();
  const { hasCapability } = useCapabilities();

  useEffect(() => {
    // If user has dashboard capability, redirect to patients
    if (hasCapability(Capability.VIEW_DASHBOARD)) {
      router.push('/patients');
      return;
    }

    // Otherwise, find the first section they can access
    const firstAccessibleSection = navigationSections.find((section) =>
      hasCapability(section.capability),
    );

    // If we found an accessible section, redirect to it
    if (firstAccessibleSection) {
      router.push(firstAccessibleSection.path);
    }
  }, [hasCapability, router]);

  // Show a loading state while redirecting
  return (
    <WithCapability
      requiredCapabilities={[
        Capability.VIEW_DASHBOARD,
        ...navigationSections.map((section) => section.capability),
      ]}
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-xl font-medium text-denim">
            You don&apos;t have permission to access any section of the
            dashboard.
          </div>
        </div>
      }
    >
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-xl font-medium text-denim">Redirecting...</div>
      </div>
    </WithCapability>
  );
}
