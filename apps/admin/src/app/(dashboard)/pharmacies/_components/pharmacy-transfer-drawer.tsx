'use client';

import { useEffect, useMemo, useState } from 'react';
import { XIcon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import { Label } from '@willow/ui/base/label';
import { toast } from '@willow/ui/base/use-toast';
import { Drawer, DrawerContent, DrawerOverlay } from '@willow/ui/base/vaul';
import { Loader } from '@willow/ui/loader';

import type { ListedPharmacy } from '~/hooks/pharmacies';
import {
  useGetEnabledPharmacies,
  useGetPharmaciesProductForms,
  useGetPharmacyPatientCountsByState,
} from '~/hooks/pharmacy';
import { useTransferPharmacies } from '~/hooks/pharmacy-transfers';

interface PharmacyTransferDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  sourcePharmacy: ListedPharmacy | null;
}

export function PharmacyTransferDrawer({
  isOpen,
  onClose,
  sourcePharmacy,
}: PharmacyTransferDrawerProps) {
  // Fetch all enabled pharmacies and states
  const { data: enabledPharmacies = [], isLoading: isLoadingPharmacies } =
    useGetEnabledPharmacies();

  // Fetch patient counts by state for the source pharmacy
  const { data: patientCountsData, isLoading: isLoadingPatientCounts } =
    useGetPharmacyPatientCountsByState(sourcePharmacy?.id || '');

  // State for form fields
  const [targetPharmacyId, setTargetPharmacyId] = useState<string>('');
  const [selectedStateIds, setSelectedStateIds] = useState<string[]>([]);

  // Fetch product forms for both source and target pharmacies
  const { isLoading: isLoadingProductForms } = useGetPharmaciesProductForms(
    sourcePharmacy?.id || '',
    targetPharmacyId,
  );

  // Reset form when drawer opens/closes or source pharmacy changes
  useEffect(() => {
    if (isOpen && sourcePharmacy) {
      setTargetPharmacyId('');
      setSelectedStateIds([]);
    }
  }, [isOpen, sourcePharmacy]);

  // Filter out the source pharmacy from available target pharmacies
  const availableTargetPharmacies = useMemo(() => {
    if (!sourcePharmacy) return [];
    return enabledPharmacies.filter(
      (pharmacy) => pharmacy.id !== sourcePharmacy.id && pharmacy.enabled,
    );
  }, [enabledPharmacies, sourcePharmacy]);

  // Get all states with patients from the source pharmacy
  const pharmacyStates = useMemo(() => {
    if (!patientCountsData?.statePatientCounts) return [];

    // Get all states that have patients, regardless of whether they're currently associated with the pharmacy
    return patientCountsData.statePatientCounts
      .filter((stateCount) => stateCount.patientCount > 0)
      .map((stateCount) => ({
        id: stateCount.stateId,
        name: stateCount.stateName,
        code: stateCount.stateCode,
        enabled: stateCount.enabled,
        isAssociated: stateCount.isAssociated,
      }));
  }, [patientCountsData]);

  // Get patient counts per state from the API
  const statePatientCounts = useMemo(() => {
    const counts: Record<string, number> = {};

    // Initialize all states to 0 patients
    pharmacyStates.forEach((state) => {
      counts[state.id] = 0;
    });

    // If we have patient counts data, use it
    if (patientCountsData?.statePatientCounts) {
      // Even if statePatientCounts is empty, the data structure is valid
      patientCountsData.statePatientCounts.forEach((stateCount) => {
        // Only set values for states with at least one patient
        if (stateCount.patientCount > 0) {
          counts[stateCount.stateId] = stateCount.patientCount;
        }
      });
    }

    return counts;
  }, [patientCountsData, pharmacyStates]);

  // Get available states for the selected target pharmacy
  const availableStates = useMemo(() => {
    if (!targetPharmacyId || !pharmacyStates.length) return [];

    // Find the selected target pharmacy
    const targetPharmacy = enabledPharmacies.find(
      (pharmacy) => pharmacy.id === targetPharmacyId,
    );

    if (!targetPharmacy?.PharmacyOnState) return [];

    // Get states that the target pharmacy serves
    const targetPharmacyStates = targetPharmacy.PharmacyOnState.map(
      (relation) => relation.state,
    ).filter((state) => state.enabled !== false); // Changed to handle cases where enabled could be undefined

    const targetPharmacyStateIds = targetPharmacyStates.map(
      (state) => state.id,
    );

    // Filter states to only include those that:
    // 1. Are served by the target pharmacy
    // 2. Have at least one patient in the source pharmacy
    return pharmacyStates.filter((state) => {
      return targetPharmacyStateIds.includes(state.id);
    });
  }, [targetPharmacyId, pharmacyStates, enabledPharmacies]);

  // Handle state selection
  const handleStateChange = (stateId: string, checked: boolean) => {
    if (checked) {
      setSelectedStateIds((prev) => [...prev, stateId]);
    } else {
      setSelectedStateIds((prev) => prev.filter((id) => id !== stateId));
    }
  };

  // Handle select all states
  const handleSelectAllStates = () => {
    if (selectedStateIds.length === availableStates.length) {
      // If all are selected, deselect all
      setSelectedStateIds([]);
    } else {
      // Otherwise, select all
      setSelectedStateIds(availableStates.map((state) => state.id));
    }
  };

  // Calculate total patients for the selected states
  const totalPatientsCount = useMemo(() => {
    return selectedStateIds.reduce((sum, stateId) => {
      return sum + (statePatientCounts[stateId] || 0);
    }, 0);
  }, [selectedStateIds, statePatientCounts]);

  const { mutateAsync: transferPharmacies, isPending } =
    useTransferPharmacies();

  // Handle transfer submission
  const handleTransferPharmacies = async () => {
    if (!sourcePharmacy || !targetPharmacyId || selectedStateIds.length === 0) {
      return;
    }

    try {
      const transferData = {
        sourcePharmacyId: sourcePharmacy.id,
        targetPharmacyId,
        stateIds: selectedStateIds,
        forms: [], // Empty array will transfer all forms
      };

      const result = await transferPharmacies(transferData);

      toast({
        title: 'Transfer Queued',
        description: `Successfully queued ${result.queuedTransfers} patient transfers.`,
      });

      onClose();
    } catch (error) {
      console.error('Error transferring pharmacies:', error);
      toast({
        title: 'Error',
        description: 'Failed to transfer patients. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Drawer
      open={isOpen}
      onOpenChange={onClose}
      direction="right"
      modal={true}
      dismissible={true}
    >
      <DrawerOverlay className="fixed inset-0 z-50 bg-black/25" />
      <DrawerContent className="fixed right-0 top-0 z-50 m-0 flex h-full w-[550px] max-w-full flex-col overflow-hidden border-l border-border bg-white p-0 shadow-lg">
        {/* Header */}
        <div className="flex h-[62px] shrink-0 items-center justify-between border-b px-6">
          <div>
            <h2 className="text-lg font-medium">Transfer Patients</h2>
            {sourcePharmacy && (
              <p className="text-sm text-gray-500">
                From {sourcePharmacy.name}
              </p>
            )}
          </div>
          <XIcon
            size={24}
            className="cursor-pointer text-gray-500"
            onClick={onClose}
          />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {isLoadingPharmacies ||
          isLoadingPatientCounts ||
          isLoadingProductForms ? (
            <div className="flex h-full items-center justify-center">
              <Loader size="lg" />
            </div>
          ) : (
            <div className="w-full space-y-6">
              {sourcePharmacy && (
                <div className="mb-6 rounded-md bg-gray-50 p-4">
                  <h3 className="text-md font-medium">Pharmacy Overview</h3>
                  <p className="mt-2 text-sm text-gray-700">
                    <span className="font-medium">Name:</span>{' '}
                    {sourcePharmacy.name}
                  </p>
                  <p className="mt-1 text-sm text-gray-700">
                    <span className="font-medium">DoseSpot ID:</span>{' '}
                    {sourcePharmacy.doseSpotPharmacyId}
                  </p>
                  <p className="mt-1 text-sm text-gray-700">
                    <span className="font-medium">Total Patients:</span>{' '}
                    <span className="rounded-full bg-denim-light/20 px-2.5 py-1 text-sm font-medium text-denim">
                      {patientCountsData?.totalPatientCount ||
                        sourcePharmacy?.patientCount ||
                        0}
                    </span>
                  </p>

                  {/* Display patient distribution by state */}
                  {pharmacyStates.length > 0 && (
                    <div className="mt-3">
                      <p className="mb-1 text-sm font-medium text-gray-700">
                        Distribution by state:
                      </p>
                      <div className="flex max-w-full flex-wrap gap-1.5">
                        {pharmacyStates
                          .sort((a, b) => a.name.localeCompare(b.name))
                          .map((state) => (
                            <span
                              key={state.id}
                              className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${state.isAssociated ? 'bg-gray-100' : 'bg-red-100'}`}
                            >
                              {state.name}:{' '}
                              <span className="ml-1 font-medium">
                                {statePatientCounts[state.id] || 0}
                              </span>
                              {!state.isAssociated && (
                                <span className="ml-1 text-xs text-red-600">
                                  *
                                </span>
                              )}
                            </span>
                          ))}
                      </div>
                      {pharmacyStates.some((state) => !state.isAssociated) && (
                        <p className="mt-1 text-xs text-red-600">
                          * States not currently associated with this pharmacy
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Target Pharmacy Selection */}
              <div className="mb-6">
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Target Pharmacy
                </label>
                <select
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                  value={targetPharmacyId}
                  onChange={(e) => setTargetPharmacyId(e.target.value)}
                  disabled={isPending}
                >
                  <option value="">Select a pharmacy</option>
                  {availableTargetPharmacies.map((pharmacy) => (
                    <option key={pharmacy.id} value={pharmacy.id}>
                      {pharmacy.name}
                    </option>
                  ))}
                </select>
                {availableTargetPharmacies.length === 0 && (
                  <p className="mt-1 text-xs text-red-500">
                    No other enabled pharmacies available.
                  </p>
                )}
              </div>

              {/* States Selection */}
              {targetPharmacyId && (
                <div className="mb-6">
                  <div className="mb-2 flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700">
                      States to Transfer
                    </label>
                    <Button
                      size="sm"
                      onClick={handleSelectAllStates}
                      disabled={availableStates.length === 0 || isPending}
                    >
                      {selectedStateIds.length === availableStates.length
                        ? 'Deselect All'
                        : 'Select All'}
                    </Button>
                  </div>

                  {availableStates.length === 0 ? (
                    <div className="rounded-md border border-dashed border-gray-300 p-4 text-center text-sm text-gray-500">
                      No common states available between these pharmacies.
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="max-h-[200px] overflow-y-auto rounded-md border border-gray-200 p-3">
                        <div className="grid grid-cols-2 gap-2">
                          {availableStates.map((state) => (
                            <div
                              key={state.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`state-${state.id}`}
                                checked={selectedStateIds.includes(state.id)}
                                className="border-gray-300 data-[state=unchecked]:bg-white"
                                onCheckedChange={(checked) =>
                                  handleStateChange(state.id, checked === true)
                                }
                                disabled={isPending}
                              />
                              <Label
                                htmlFor={`state-${state.id}`}
                                className="flex cursor-pointer items-center text-sm font-normal"
                              >
                                {state.name}
                                {!state.isAssociated && (
                                  <span className="ml-1 text-xs text-red-600">
                                    *
                                  </span>
                                )}
                                <span
                                  className={`ml-1.5 inline-flex items-center rounded-full px-1.5 py-0.5 text-xs font-medium ${state.isAssociated ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800'}`}
                                >
                                  {statePatientCounts[state.id] || 0}
                                </span>
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                      {availableStates.some((state) => !state.isAssociated) && (
                        <p className="text-xs text-red-600">
                          * States not currently associated with the source
                          pharmacy
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Display total patients count for selected states */}
              {selectedStateIds.length > 0 && totalPatientsCount > 0 && (
                <div className="mb-6 rounded-md bg-gray-50 p-3">
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">
                      {totalPatientsCount} patients
                    </span>{' '}
                    will be transferred from the selected states
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex shrink-0 justify-end space-x-3 border-t p-6">
          <Button onClick={onClose} disabled={isPending}>
            Cancel
          </Button>
          <Button
            onClick={handleTransferPharmacies}
            disabled={
              !sourcePharmacy ||
              !targetPharmacyId ||
              selectedStateIds.length === 0 ||
              isPending
            }
          >
            {isPending ? <Loader className="mr-2 h-4 w-4" /> : null}
            Queue Immediate Transfer
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
