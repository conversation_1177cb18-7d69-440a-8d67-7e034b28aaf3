name: production-deploy
on:
  push:
    paths:
      - apps/api/**
      - packages/db/**
      - packages/utils/**
      - packages/auth/**
      - .github/workflows/api-production-deploy.yml
    branches:
      - main
  workflow_dispatch:

concurrency: main-deploy

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@v2
        with:
          ref: main

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          registry-type: private

      - name: Get current date
        id: date
        run: 'echo "::set-output name=date::$(date +''%s'')"'

      # - name: Create env file
      #   run: |
      #     echo "${{ secrets.PROD_ALL_ENV_VARS }}" > .env

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:latest

      - name: Login to Amazon ECR (before build)
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push images to Amazon ECR
        id: build-api-image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/api/Dockerfile.prod
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ vars.API_ECR_REPOSITORY }}:production
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64
          provenance: false

      - name: Set Color and Deploy Status for Build Failure
        if: failure()
        run: |
          echo "color=#ff0000" >> $GITHUB_ENV
          echo "deployStatus=Build failed! ❌" >> $GITHUB_ENV

      - name: Run Database Migration
        id: rds-db-migration
        env:
          DATABASE_URL: ${{ secrets.API_PROD_DATABASE_URL }}
        run: |
          npx prisma migrate deploy --schema=packages/db/prisma/schema.prisma

      - name: Set Color and Deploy Status for Database Migration Failure
        if: failure()
        run: |
          echo "color=#ff0000" >> $GITHUB_ENV
          echo "deployStatus=Database migration failed! ❌" >> $GITHUB_ENV

      - name: Download task definition
        env:
          TASK_DEF: ${{ vars.PROD_TASK_DEF }}
        run: |
          aws ecs describe-task-definition --task-definition $TASK_DEF \
          --query taskDefinition > task-definition.json

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: task-definition.json
          cluster: ${{ vars.API_PROD_CLUSTERNAME }}
          service: ${{ vars.API_PROD_SERVICENAME }}
          wait-for-service-stability: true
          wait-for-minutes: 15

      - name: Set Color and Deploy Status for Success
        if: success()
        run: |
          echo "color=#008000" >> $GITHUB_ENV
          echo "deployStatus=Deployment successful! 🚀" >> $GITHUB_ENV

      - name: Set Color and Deploy Status for Failure
        if: failure()
        run: |
          echo "color=#ff0000" >> $GITHUB_ENV
          echo "deployStatus=Deployment failed! ❌" >> $GITHUB_ENV

      # - name: Send Slack Notifications
      #   if: always()
      #   uses: outliant/slack-notification/@main
      #   with:
      #     commitUrl: "https://github.com/${{ github.repository }}/commit/${{ github.sha }}"
      #   env:
      #     ENVIRONMENT: Production
      #     COLOR_CODE: ${{ env.color }}
      #     DEPLOY_STATUS: ${{ env.deployStatus }}
      #     LOG_STREAM_URL: ${{ vars.PROD_LOG_STREAM_URL }}
      #     SERVICE_NAME: "Production API"
      #     SERVICE_LINK: ${{ vars.PROD_SERVICE_LINK }}
      #     SERVICE_TYPE: "ECS/Fargate"
      #     SLACK_WEBHOOK_URL: ${{ vars.SLACK_WEBHOOK_URL}}
